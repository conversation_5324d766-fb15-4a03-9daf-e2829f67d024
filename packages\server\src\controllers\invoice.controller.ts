import {
  achPull,
  Company,
  crypt,
  CustomerAccount,
  Invoice,
  LoanApplication,
  Operation,
  User,
  UserRole,
} from '@linqpal/common-backend'
import {
  EInvoiceType,
  exceptions,
  IEncrypted,
  InvoicePaymentType,
} from '@linqpal/models'
import moment from 'moment-timezone'
import { parsePhoneNumber } from 'libphonenumber-js'
import {
  invoiceSchemaStatus,
  invoiceStatus,
  LOAN_APPLICATION_STATUS,
  OPERATION_STATUS,
  OPERATION_TYPES,
  PAYMENT_INITIATOR,
  PROCESSING_STATUSES,
  statesHashMap,
} from '@linqpal/models/src/dictionaries'
import { IInvoice } from '@linqpal/common-backend/src/models/types'
import { FilterQuery, PipelineStage, Types } from 'mongoose'
import { States } from '@linqpal/models/src/dictionaries/states'
import { Request } from 'express'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'

const format = '%m/%d/%Y'
const timezone = 'America/Chicago'
const cstNow = moment().tz('America/Chicago').toDate()
const ACHdiscountAllowedStatuses = [
  invoiceStatus.due,
  invoiceStatus.paymentFailed,
  invoiceStatus.applicationRejected,
  invoiceStatus.applicationCancelled,
]

const isDueQuery = () => {
  return {
    $and: [
      {
        $or: [
          {
            $and: [
              { $ne: ['$expiration_date', null] },
              { $lte: [cstNow, '$expiration_date'] },
            ],
          },
          {
            $and: [{ $eq: ['$expiration_date', null] }],
          },
        ],
      },
      { $lte: [cstNow, '$invoice_due_date'] },
    ],
  }
}

// TODO: VK: Review status calculation for admin, suppliers and customers, split them if possible
export function projectInvoiceStatus(
  ownerId: string | null,
  statusType?: 'customer' | 'any',
) {
  return {
    $switch: {
      branches: [
        // use original status for quotes
        {
          case: { $eq: ['$type', EInvoiceType.QUOTE] },
          then: '$status',
        },
        // invoices
        {
          case: {
            $and: [
              {
                $or: [
                  { $eq: ['$operation', null] },
                  { $eq: ['$operation.status', OPERATION_STATUS.PLACED] },
                ],
              },
              { $ifNull: ['$expiration_date', false] },
              { $gt: [cstNow, '$expiration_date'] },
            ],
          },
          then: invoiceStatus.expired,
        },
        {
          case: {
            $or: [
              { $eq: ['$status', invoiceStatus.dismissed] },
              { $eq: ['$status', invoiceStatus.cancelled] },
            ],
          },
          then: '$status',
        },
        {
          case: {
            $and: [
              { $ne: [ownerId, null] },
              { $eq: ['$loanApp.status', LOAN_APPLICATION_STATUS.CANCELED] },
              {
                $or: [
                  { $eq: ['$operationStatus', ''] },
                  { $eq: ['$operationStatus', OPERATION_STATUS.PLACED] },
                ],
              },
            ],
          },
          then: invoiceStatus.applicationCancelled,
        },
        {
          case: {
            $and: [
              { $ne: [ownerId, null] },
              { $eq: ['$loanApp.status', LOAN_APPLICATION_STATUS.REJECTED] },
              {
                $or: [
                  { $eq: ['$operationStatus', ''] },
                  { $eq: ['$operationStatus', OPERATION_STATUS.PLACED] },
                ],
              },
            ],
          },
          then: invoiceStatus.applicationRejected,
        },
        statusType === 'customer'
          ? {
              // for customers display Credit Applied status instead of Pending Disbursement / Approved / Invoiced
              case: {
                $and: [
                  { $ne: [ownerId, null] },
                  {
                    $or: [
                      {
                        $eq: [
                          '$loanApp.status',
                          LOAN_APPLICATION_STATUS.APPROVED,
                        ],
                      },
                      {
                        $and: [
                          {
                            $eq: [
                              '$quote.status',
                              invoiceSchemaStatus.invoiced,
                            ],
                          },
                          {
                            $ne: [
                              '$paymentDetails.paymentType',
                              InvoicePaymentType.FACTORING,
                            ],
                          },
                        ],
                      },
                      // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
                      // calculated pending disbursement status
                      {
                        $and: [
                          {
                            $eq: [
                              '$loanApp.status',
                              LOAN_APPLICATION_STATUS.PROCESSING,
                            ],
                          },
                          { $gte: ['$loanApp.lms_id', ' '] },
                          {
                            $eq: [
                              '$loanApp.metadata.repayment.autoTradeCreditEnabled',
                              true,
                            ],
                          },
                          {
                            $or: [
                              { $eq: ['$operationStatus', ''] },
                              {
                                $eq: [
                                  '$operationStatus',
                                  OPERATION_STATUS.PLACED,
                                ],
                              },
                            ],
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
              then: invoiceStatus.creditApplied,
            }
          : {
              // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
              case: {
                $and: [
                  { $ne: [ownerId, null] },
                  {
                    $eq: [
                      '$loanApp.status',
                      LOAN_APPLICATION_STATUS.PROCESSING,
                    ],
                  },
                  { $gte: ['$loanApp.lms_id', ' '] },
                  {
                    $eq: [
                      '$loanApp.metadata.repayment.autoTradeCreditEnabled',
                      true,
                    ],
                  },
                  {
                    $or: [
                      { $eq: ['$operationStatus', ''] },
                      { $eq: ['$operationStatus', OPERATION_STATUS.PLACED] },
                    ],
                  },
                ],
              },
              then: invoiceStatus.pendingDisbursement,
            },
        {
          case: {
            $and: [
              { $ne: [ownerId, null] },
              { $in: ['$loanApp.status', PROCESSING_STATUSES] },
              {
                $or: [
                  { $eq: ['$operationStatus', ''] },
                  { $eq: ['$operationStatus', OPERATION_STATUS.PLACED] },
                ],
              },
            ],
          },
          then: invoiceStatus.applicationProcessing,
        },
        {
          case: {
            $and: [
              { $eq: ['$payer_id', ownerId] },
              { $eq: ['$status', invoiceStatus.draft] },
            ],
          },
          then: invoiceStatus.placed,
        },
        {
          case: {
            $and: [
              { $eq: [ownerId, null] },
              {
                $or: [
                  { $eq: ['$status', invoiceStatus.draft] },
                  {
                    $and: [
                      { $eq: ['$status', invoiceStatus.placed] },
                      { $ifNull: ['$supplierInvitationDetails', false] },
                      {
                        $or: [
                          { $eq: ['$company_id', ''] },
                          { $eq: ['$company_id', null] },
                        ],
                      },
                    ],
                  },
                ],
              },
            ],
          },
          then: 'UPLOADED',
        },
        {
          case: { $ne: ['$status', invoiceStatus.placed] },
          then: '$status',
        },
        {
          case: {
            $and: [
              {
                $or: [
                  { $eq: ['$operation', null] },
                  { $eq: ['$operation.status', OPERATION_STATUS.PLACED] },
                ],
              },
              {
                $or: [
                  {
                    $and: [
                      { $ne: [{ $ifNull: ['$expiration_date', null] }, null] },
                      { $lte: [cstNow, '$expiration_date'] },
                    ],
                  },
                  { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
                ],
              },
              { $lte: [cstNow, '$invoice_due_date'] },
              {
                $and: [
                  { $ne: [ownerId, null] },
                  { $ne: ['$company_id', ownerId] },
                ],
              },
            ],
          },
          then: invoiceStatus.due,
        },
        {
          case: {
            $and: [
              {
                $or: [
                  { $eq: ['$operation', null] },
                  { $eq: ['$operation.status', OPERATION_STATUS.PLACED] },
                ],
              },
              {
                $or: [
                  {
                    $and: [
                      { $ne: [{ $ifNull: ['$expiration_date', null] }, null] },
                      { $lte: [cstNow, '$expiration_date'] },
                    ],
                  },
                  { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
                ],
              },
              { $lte: [cstNow, '$invoice_due_date'] },
              {
                $and: [
                  { $ne: ['$payer_id', ownerId] },
                  { $eq: ['$company_id', ownerId] },
                ],
              },
              { $eq: ['$seen', true] },
            ],
          },
          then: invoiceStatus.seen,
        },
        {
          case: {
            $and: [
              {
                $or: [
                  { $eq: ['$operation', null] },
                  { $eq: ['$operation.status', OPERATION_STATUS.PLACED] },
                  {
                    $and: [
                      {
                        $eq: ['$operation.status', OPERATION_STATUS.PROCESSING],
                      },
                      {
                        $eq: [
                          '$paymentDetails.paymentType',
                          InvoicePaymentType.FACTORING,
                        ],
                      },
                      {
                        $ne: [
                          {
                            $round: [
                              {
                                $subtract: [
                                  {
                                    $add: [
                                      { $ifNull: ['$operation.amount', 0] },
                                      { $round: ['$lateFee', 2] },
                                    ],
                                  },
                                  {
                                    $add: [
                                      { $ifNull: ['$operation.paidAmount', 0] },
                                      {
                                        $ifNull: [
                                          '$operation.processingAmount',
                                          0,
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                              2,
                            ],
                          },
                          0,
                        ],
                      },
                    ],
                  },
                ],
              },
              {
                $or: [
                  {
                    $and: [
                      { $ne: [{ $ifNull: ['$expiration_date', null] }, null] },
                      { $lt: [cstNow, '$expiration_date'] },
                    ],
                  },
                  { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
                ],
              },
              { $gt: [cstNow, '$invoice_due_date'] },
            ],
          },
          then: invoiceStatus.pastDue,
        },
        {
          case: {
            $and: [
              { $eq: ['$operation.type', OPERATION_TYPES.INVOICE.PAYMENT] },
              { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
              {
                $eq: [
                  '$operation.metadata.paymentInitiator',
                  PAYMENT_INITIATOR.COMPANY,
                ],
              },
            ],
          },
          then: invoiceStatus.collected,
        },
        {
          case: {
            $and: [
              { $eq: ['$operation.type', OPERATION_TYPES.INVOICE.PAYMENT] },
              { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
            ],
          },
          then: invoiceStatus.paid,
        },
        {
          case: {
            $and: [
              { $eq: ['$operation.type', OPERATION_TYPES.INVOICE.REFUND] },
              { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
            ],
          },
          then: invoiceStatus.refunded,
        },
        statusType === 'customer'
          ? {
              case: { $eq: ['$operation.status', OPERATION_STATUS.PROCESSING] },
              then: {
                $cond: {
                  if: {
                    $eq: [
                      {
                        $round: [
                          {
                            $subtract: [
                              {
                                $add: [
                                  '$operation.amount',
                                  { $round: ['$lateFee', 2] },
                                  {
                                    $ifNull: ['$paymentDetails.customerFee', 0],
                                  },
                                ],
                              },
                              { $ifNull: ['$operation.paidAmount', 0] },
                            ],
                          },
                          2,
                        ],
                      },
                      0,
                    ],
                  },
                  then: invoiceStatus.paid,
                  else: invoiceStatus.due,
                },
              },
            }
          : {
              case: { $eq: ['$operation.status', OPERATION_STATUS.PROCESSING] },
              then: {
                $cond: {
                  if: {
                    $and: [
                      {
                        $eq: [
                          '$paymentDetails.paymentType',
                          InvoicePaymentType.FACTORING,
                        ],
                      },
                      {
                        $eq: [
                          '$paymentDetails.arAdvanceStatus',
                          ArAdvanceStatus.Approved,
                        ],
                      },
                      {
                        $or: [
                          {
                            $ne: [
                              { $ifNull: ['$operation.paidAmount', null] },
                              null,
                            ],
                          },
                          {
                            $ne: [
                              {
                                $ifNull: ['$operation.processingAmount', null],
                              },
                              null,
                            ],
                          },
                        ],
                      },
                    ],
                  },
                  then: {
                    $cond: {
                      if: {
                        $lte: [
                          {
                            $round: [
                              {
                                $subtract: [
                                  '$operation.amount',
                                  { $ifNull: ['$operation.paidAmount', 0] },
                                ],
                              },
                              2,
                            ],
                          },
                          0,
                        ],
                      },
                      then: invoiceStatus.paid,
                      else: invoiceStatus.due,
                    },
                  },
                  else: invoiceStatus.paymentProcessing,
                },
              },
            },
        {
          case: { $eq: ['$operation.status', OPERATION_STATUS.FAIL] },
          then: invoiceStatus.paymentFailed,
        },

        // handling partial payment, when the first transaction has been processed
        {
          case: {
            $and: [
              { $eq: ['$operation.type', OPERATION_TYPES.INVOICE.PAYMENT] },
              { $eq: ['$operation.status', OPERATION_STATUS.PLACED] },
              {
                $or: [
                  { $gt: [{ $ifNull: ['$operation.paidAmount', 0] }, 0] },
                  { $gt: [{ $ifNull: ['$operation.processingAmount', 0] }, 0] },
                ],
              },
              {
                $eq: [
                  '$paymentDetails.paymentType',
                  InvoicePaymentType.FACTORING,
                ],
              },
            ],
          },
          then: invoiceStatus.due,
        },
      ],
      default: '$status',
    },
  }
}

export const makeCardRefund = async (req: Request) => {
  const { amount, operationId, invoiceId } = req.body

  // get operation and invoice
  const operation = await Operation.findById(operationId).populate(
    'transactions',
  )
  if (!operation) throw new exceptions.LogicalError('Operation not found')
  const invoice = await Invoice.findById(operation.owner_id)
  if (!invoice) throw new exceptions.LogicalError('invoice/not-found')
  const { payee_id, payer_id } = operation.metadata

  // get supplier's primary bank account details
  const supplierCompany = await Company.findById(payee_id).populate(
    'bankAccounts',
  )
  const supplierPrimaryBankAccount = supplierCompany?.bankAccounts?.filter(
    (b) => b.isPrimary,
  )
  if (!supplierPrimaryBankAccount?.length) {
    throw new exceptions.LogicalError(
      `supplier '${supplierCompany?.name}' does not have a primary bank account`,
    )
  }

  const [refundOperation] = await Operation.create(
    {
      owner_id: invoiceId,
      amount: amount,
      date: moment().toDate(),
      type: OPERATION_TYPES.INVOICE.REFUND,
      status: OPERATION_STATUS.PLACED,
      metadata: { payer_id: payee_id, payee_id: payer_id },
    },
    { session: req.session },
  )
  const einEncrypted = supplierCompany?.ein as IEncrypted
  const accountEncrypted = supplierPrimaryBankAccount?.[0]
    .accountNumber as IEncrypted
  const achSender = {
    customer: {
      userType: 'BUSINESS',
      firstName: supplierCompany?.name,
      identificationType: 'EIN',
      identification: await crypt.decrypt(einEncrypted.cipher),
    },
    customerAccount: {
      identification: await crypt.decrypt(accountEncrypted.cipher),
      identificationType2:
        supplierPrimaryBankAccount?.[0].accountType?.toUpperCase(),
      institution: {
        name: supplierPrimaryBankAccount?.[0].name,
        identification: supplierPrimaryBankAccount?.[0].routingNumber || '',
      },
    },
    customerContact: {
      primaryEmail: supplierCompany?.email,
      primaryPhone: parsePhoneNumber(supplierCompany?.phone || '', 'US')
        .nationalNumber as string,
    },
    customerPostalAddress: {
      addressLine1: supplierCompany?.address?.address,
      city: supplierCompany?.address?.city,
      state: supplierCompany?.address?.state
        ? statesHashMap[supplierCompany.address.state as States]
        : '',
      zipCode: supplierCompany?.address?.zip,
    },
  }

  await achPull(
    refundOperation,
    {
      amount: invoice.total_amount, //invoiceamount + fee
      fee: 0.0,
      currency: 'USD',
      ...achSender,
      transactionDateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      reason: 'Invoice refund',
    },
    req.session,
  )

  return refundOperation.id
}

export function getStartDate(period: string) {
  const p = parseInt(period, 10)
  return moment()
    .subtract(p, p === 1 ? 'years' : 'days')
    .startOf('day')
    .toDate()
}

export const outstandingInvoices = async (
  company_id: string,
  period: string | undefined,
) => {
  const STATUSES = [
    invoiceStatus.placed,
    invoiceStatus.pastDue,
    invoiceStatus.paymentPosted,
  ]
  if (!period) {
    const pipeline = [
      {
        $match: {
          $and: [
            { status: { $in: STATUSES } },
            { isDeleted: { $ne: true } },
            { company_id: { $eq: company_id } },
          ],
        },
      },
      {
        $lookup: {
          from: Operation.collection.name,
          as: 'operation',
          let: { invoice_id: { $toString: '$_id' } },
          pipeline: [
            { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
            {
              $match: {
                status: {
                  $in: [
                    OPERATION_STATUS.PLACED,
                    OPERATION_STATUS.PROCESSING,
                    OPERATION_STATUS.SUCCESS,
                    OPERATION_STATUS.FAIL,
                  ],
                },
                type: OPERATION_TYPES.INVOICE.PAYMENT,
              },
            },
          ],
        },
      },
      { $unwind: { path: '$operation', preserveNullAndEmptyArrays: true } },
      {
        $project: {
          invoice_date: 1,
          status: projectInvoiceStatus(company_id),
          total_amount: 1,
          payer_id: 1,
          company_id: 1,
          invoice_number: 1,
          operation_id: 1,
          customer_account_id: 1,
          material_description: 1,
          note: 1,
          material_subtotal: 1,
          tax_amount: 1,
          refunded_amount: 1,
          invoice_due_date: 1,
          expiration_date: 1,
          address: 1,
          addressType: 1,
          invoice_document: 1,
        },
      },
      {
        $match: {
          $and: [{ status: { $in: STATUSES } }],
        },
      },
    ]
    return Invoice.aggregate(pipeline)
  }

  const startDate = getStartDate(period)
  const pipeline = [
    {
      $match: {
        $and: [
          { status: { $in: STATUSES } },
          { isDeleted: { $ne: true } },
          { company_id: { $eq: company_id } },
          { invoice_date: { $gte: startDate } },
        ],
      },
    },
    {
      $lookup: {
        from: Operation.collection.name,
        as: 'operation',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
          {
            $match: {
              status: {
                $in: [
                  OPERATION_STATUS.PLACED,
                  OPERATION_STATUS.PROCESSING,
                  OPERATION_STATUS.SUCCESS,
                  OPERATION_STATUS.FAIL,
                ],
              },
              type: OPERATION_TYPES.INVOICE.PAYMENT,
            },
          },
        ],
      },
    },
    { $unwind: { path: '$operation', preserveNullAndEmptyArrays: true } },
    {
      $project: {
        invoice_date: 1,
        status: projectInvoiceStatus(company_id),
        total_amount: 1,
      },
    },
    {
      $match: {
        $and: [{ status: { $in: STATUSES } }],
      },
    },
    {
      $project: {
        month: { $month: '$invoice_date' },
        year: { $year: '$invoice_date' },
        ym: {
          $concat: [
            { $toString: { $year: '$invoice_date' } },
            { $toString: { $month: '$invoice_date' } },
          ],
        },
        total_amount: 1,
      },
    },
    {
      $group: {
        _id: '$ym',
        avgAmount: { $avg: '$total_amount' },
      },
    },
  ]
  return Invoice.aggregate(pipeline)
}

export async function getInvoice({
  _id,
  invoiceNumber,
  supplierCompanyId,
  companyId = '',
  statusType,
}: {
  _id?: string
  invoiceNumber?: string
  supplierCompanyId?: string
  companyId?: string
  statusType?: 'customer' | 'any'
}) {
  if (!_id && !(invoiceNumber && supplierCompanyId)) {
    throw new Error(
      "Either '_id' must be provided, or both 'invoiceNumber' and 'supplierCompanyId' must be provided.",
    )
  }

  const matchCondition: Record<string, any> = {}
  if (_id) matchCondition._id = new Types.ObjectId(_id)
  if (invoiceNumber) matchCondition.invoice_number = invoiceNumber
  if (supplierCompanyId) matchCondition.company_id = supplierCompanyId

  const pipeline: PipelineStage[] = [
    { $match: matchCondition },
    {
      $addFields: {
        supplier_id: {
          $convert: {
            input: '$company_id',
            to: 'objectId',
            onError: null,
          },
        },
      },
    },
    {
      $lookup: {
        from: Company.collection.name,
        as: 'company',
        localField: 'supplier_id',
        foreignField: '_id',
      },
    },
    { $unwind: { path: '$company', preserveNullAndEmptyArrays: true } },
    { $unset: ['company.bankAccounts'] },
    {
      $addFields: {
        lateFee: {
          $cond: {
            if: {
              $eq: [
                '$company.settings.arAdvance.isLateInterestChargedToMerchant',
                false,
              ],
            },
            then: {
              $ifNull: ['$paymentDetails.fees', 0],
            },
            else: 0,
          },
        },
      },
    },

    {
      $lookup: {
        from: Invoice.collection.name,
        as: 'quote',
        let: {
          quoteId: {
            $convert: {
              input: '$quoteId',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [{ $match: { $expr: { $eq: ['$$quoteId', '$_id'] } } }],
      },
    },
    { $unwind: { path: '$quote', preserveNullAndEmptyArrays: true } },
    {
      // review and remove this extra projection
      $project: {
        _id: 1,
        company: 1,
        company_id: 1,
        payer_id: 1,
        customer_account_id: 1,
        invoice_document: 1,
        supplierInvitationDetails: 1,
        document_name: 1,
        invoice_date: 1,
        invoice_due_date: 1,
        expiration_date: 1,
        address: 1,
        country: 1,
        unitNumber: 1,
        city: 1,
        state: 1,
        zip: 1,
        addressType: 1,
        attention: 1,
        customer_user_id: 1,
        invoice_number: 1,
        total_amount: 1,
        material_subtotal: 1,
        tax_amount: 1,
        material_description: 1,
        paymentDetails: 1,
        transaction_ids: 1,
        note: 1,
        createdAt: 1,
        status: 1,
        type: 1,
        project_id: 1,
        quote: 1,
        quoteId: 1,
        quote_number: 1,
        connector: 1,
        lateFee: 1,
      },
    },
    {
      $lookup: {
        from: Operation.collection.name,
        as: 'operation',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
          {
            $match: {
              status: {
                $in: [
                  OPERATION_STATUS.PLACED,
                  OPERATION_STATUS.PROCESSING,
                  OPERATION_STATUS.SUCCESS,
                  OPERATION_STATUS.FAIL,
                ],
              },
              type: OPERATION_TYPES.INVOICE.PAYMENT,
            },
          },
        ],
      },
    },
    { $unwind: { path: '$operation', preserveNullAndEmptyArrays: true } },
    {
      $project: {
        _id: 1,
        company_id: 1,
        company: 1,
        customer_account_id: 1,
        invoice_document: 1,
        document_name: 1,
        invoice_date: {
          $dateToString: { date: '$invoice_date', format, timezone },
        },
        invoice_due_date: {
          $dateToString: { date: '$invoice_due_date', format, timezone },
        },
        expiration_date: {
          $dateToString: { date: '$expiration_date', format, timezone },
        },
        address: {
          $cond: [
            { $gt: ['$address', ''] },
            '$address',
            {
              $concat: [
                {
                  $cond: [{ $gt: ['$country', ''] }, '$country', ''],
                },
                {
                  $cond: [
                    {
                      $and: [
                        { $gt: ['$city', ''] },
                        { $lte: ['$country', ''] },
                      ],
                    },
                    '$city',
                    {
                      $cond: [
                        { $gt: ['$city', ''] },
                        { $concat: [', ', '$city'] },
                        '',
                      ],
                    },
                  ],
                },
                {
                  $cond: [
                    { $gt: ['$state', ''] },
                    { $concat: [', ', '$state'] },
                    '',
                  ],
                },
                {
                  $cond: [
                    { $gt: ['$zip', ''] },
                    { $concat: [', ', '$zip'] },
                    '',
                  ],
                },
              ],
            },
          ],
        },
        addressType: 1,
        attention: 1,
        customer_user_id: 1,
        invoice_number: 1,
        total_amount: 1,
        material_subtotal: 1,
        tax_amount: 1,
        material_description: 1,
        transaction_ids: 1,
        note: 1,
        createdAt: 1,
        payer_id: 1,
        supplierInvitationDetails: 1,
        status: projectInvoiceStatus(companyId, statusType),
        operation: 1,
        type: 1,
        achDiscountValidUpto: {
          $dateAdd: {
            startDate: '$invoice_date',
            unit: 'day',
            amount: '$company.settings.achDiscount.validityInDays',
          },
        },
        paymentDetails: 1,
        paymentType: '$paymentDetails.paymentType',
        arAdvanceStatus: '$paymentDetails.arAdvanceStatus',
        isDue: isDueQuery(),
        project_id: 1,
        quote: 1,
        quoteId: 1,
        quote_number: 1,
        connector: 1,
        lateFee: 1,
      },
    },
  ]
  pipeline.push({
    $addFields: {
      achDiscount: {
        $cond: [
          {
            $and: [
              { $gt: ['$total_amount', 0] },
              { $eq: ['$isDue', true] },
              { $in: ['$status', ACHdiscountAllowedStatuses] },
              { $lt: [cstNow, '$achDiscountValidUpto'] },
            ],
          },
          '$company.settings.achDiscount.percentage',
          0,
        ],
      },
    },
  })
  const invoice = await Invoice.aggregate(pipeline)
  const result = invoice.length > 0 && {
    ...invoice[0],
    achDiscount: (invoice[0].total_amount * invoice[0].achDiscount) / 100,
  }
  return invoice.length > 0 ? result : undefined
}

export async function getInvoicesStatus(invoicesId: string[]) {
  const pipeline: PipelineStage[] = [
    {
      $match: { _id: { $in: invoicesId.map((id) => new Types.ObjectId(id)) } },
    },
    {
      $addFields: {
        supplier_id: {
          $convert: {
            input: '$company_id',
            to: 'objectId',
            onError: null,
          },
        },
      },
    },
    {
      $lookup: {
        from: Company.collection.name,
        as: 'company',
        localField: 'supplier_id',
        foreignField: '_id',
      },
    },
    { $unwind: { path: '$company', preserveNullAndEmptyArrays: true } },
    { $unset: ['company.bankAccounts'] },
    {
      $lookup: {
        from: Operation.collection.name,
        as: 'operation',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
          {
            $match: {
              status: {
                $in: [
                  OPERATION_STATUS.PLACED,
                  OPERATION_STATUS.PROCESSING,
                  OPERATION_STATUS.SUCCESS,
                  OPERATION_STATUS.FAIL,
                ],
              },
              type: OPERATION_TYPES.INVOICE.PAYMENT,
            },
          },
        ],
      },
    },
    { $unwind: { path: '$operation', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: LoanApplication.collection.name,
        as: 'loanApp',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          {
            $addFields: {
              ids: {
                $cond: {
                  if: {
                    $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                  },
                  then: '$invoiceDetails.invoiceId',
                  else: ['$invoiceDetails.invoiceId'],
                },
              },
            },
          },
          {
            $match: {
              $expr: {
                $in: ['$$invoice_id', '$ids'],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
          {
            $project: {
              status: 1,
            },
          },
        ],
      },
    },
    { $unwind: { path: '$loanApp', preserveNullAndEmptyArrays: true } },
    {
      $project: {
        _id: 1,
        status: projectInvoiceStatus('$company_id'),
      },
    },
  ]

  return Invoice.aggregate(pipeline)
}

export async function getBuilderInvoices(req: Request) {
  const DUE_STATUSES = [
    invoiceStatus.due,
    invoiceStatus.pastDue,
    invoiceStatus.placed,
  ]
  const builderId = req.company!._id.toString()
  const isActionRequired = req.query.actionRequired || false

  const userRoles = await UserRole.aggregate([
    { $match: { company_id: builderId } },
    {
      $lookup: {
        from: User.collection.name,
        as: 'user',
        localField: 'sub',
        foreignField: 'sub',
      },
    },
    { $unwind: '$user' },
    { $sort: { 'user.login': 1 } },
  ])

  const matchConditions = userRoles.map(
    (userRole) => userRole.user.email || userRole.user.login,
  )

  const customerAccounts = await CustomerAccount.aggregate([
    {
      $match: {
        $or: [
          { email: { $in: matchConditions } },
          { phone: { $in: matchConditions } },
        ],
      },
    },
  ])

  const customerAccountIds = customerAccounts.map((ca) => ca._id.toString())

  const invoiceInterval = moment().subtract(6, 'months').toDate()

  const pipeline: PipelineStage[] = [
    {
      $match: {
        $and: [
          { createdAt: { $gte: invoiceInterval } },
          {
            $or: [
              { customer_account_id: { $in: customerAccountIds } },
              { payer_id: builderId },
            ],
          },
          {
            $nor: [
              {
                type: EInvoiceType.QUOTE,
                'paymentDetails.paymentType': InvoicePaymentType.FACTORING,
              },
            ],
          },
        ],
      },
    },
    {
      $lookup: {
        from: CustomerAccount.collection.name,
        as: 'customer',
        let: {
          customer_account_id: {
            $convert: {
              input: '$customer_account_id',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$_id', '$$customer_account_id'],
              },
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$customer',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        isDeleted: { $ne: true },
        $or: [{ status: { $ne: invoiceStatus.draft } }, { company_id: '' }],
      },
    },
    { $addFields: { customer_id: { $toString: '$_id' } } },
    {
      $addFields: {
        supplier_id: {
          $convert: {
            input: '$company_id',
            to: 'objectId',
            onError: null,
          },
        },
      },
    },
    { $addFields: { createdDate: { $toDate: '$createdAt' } } },
    { $sort: { createdAt: -1 } },
    {
      $lookup: {
        from: Company.collection.name,
        as: 'company',
        localField: 'supplier_id',
        foreignField: '_id',
      },
    },
    { $unwind: { path: '$company', preserveNullAndEmptyArrays: true } },

    { $unset: ['company.bankAccounts'] },
    {
      $addFields: {
        lateFee: {
          $cond: {
            if: {
              $eq: [
                '$company.settings.arAdvance.isLateInterestChargedToMerchant',
                false,
              ],
            },
            then: {
              $ifNull: ['$paymentDetails.fees', 0],
            },
            else: 0,
          },
        },
      },
    },

    {
      $lookup: {
        from: Operation.collection.name,
        as: 'operation',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
          {
            $match: {
              $expr: {
                $or: [
                  {
                    $and: [
                      {
                        $in: [
                          '$status',
                          [
                            OPERATION_STATUS.PLACED,
                            OPERATION_STATUS.PROCESSING,
                            OPERATION_STATUS.SUCCESS,
                            OPERATION_STATUS.FAIL,
                          ],
                        ],
                      },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.PAYMENT] },
                    ],
                  },
                  {
                    $and: [
                      { $eq: ['$status', OPERATION_STATUS.SUCCESS] },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.REFUND] },
                    ],
                  },
                ],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
        ],
      },
    },
    { $unwind: { path: '$operation', preserveNullAndEmptyArrays: true } },

    {
      $lookup: {
        from: LoanApplication.collection.name,
        as: 'loanApp',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          {
            $addFields: {
              ids: {
                $cond: {
                  if: {
                    $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                  },
                  then: '$invoiceDetails.invoiceId',
                  else: ['$invoiceDetails.invoiceId'],
                },
              },
            },
          },
          {
            $match: {
              $expr: {
                $in: ['$$invoice_id', '$ids'],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
          {
            $project: {
              status: 1,
            },
          },
        ],
      },
    },
    { $unwind: { path: '$loanApp', preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        totalPaidAmount: {
          $cond: [
            { $ifNull: ['$operation.paidAmount', false] },
            '$operation.paidAmount',
            {
              $cond: [
                { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalProcessingAmount: {
          $cond: [
            { $ifNull: ['$operation.processingAmount', false] },
            '$operation.processingAmount',
            {
              $cond: [
                {
                  $and: [
                    {
                      $eq: ['$operation.status', OPERATION_STATUS.PROCESSING],
                    },

                    { $eq: ['$totalPaidAmount', 0] },
                  ],
                },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalRemainingAmount: {
          $round: [
            {
              $subtract: [
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$operation.amount', '$total_amount'] },
                        { $ifNull: ['$lateFee', 0] },
                      ],
                    },
                    2,
                  ],
                },
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$totalPaidAmount', 0] },
                        { $ifNull: ['$totalProcessingAmount', 0] },
                      ],
                    },
                    2,
                  ],
                },
              ],
            },
            2,
          ],
        },
      },
    },
    {
      $lookup: {
        from: Invoice.collection.name,
        as: 'quote',
        let: {
          quoteId: {
            $convert: {
              input: '$quoteId',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $ne: ['$$quoteId', null] },
                  { $eq: ['$_id', '$$quoteId'] },
                ],
              },
            },
          },
        ],
      },
    },
    { $unwind: { path: '$quote', preserveNullAndEmptyArrays: true } },
    { $addFields: { operationStatus: { $ifNull: ['$operation.status', ''] } } },
    {
      $project: {
        _id: 1,
        company_id: 1,
        invoice_document: 1,
        invoice_date: {
          $dateToString: { date: '$invoice_date', format, timezone },
        },
        invoice_due_date: {
          $dateToString: { date: '$invoice_due_date', format, timezone },
        },
        expiration_date: {
          $dateToString: { date: '$expiration_date', format, timezone },
        },
        quoteDetails: 1,
        address: {
          $concat: [
            '$address',
            {
              $cond: [
                { $lte: ['$city', ''] },
                '',
                { $concat: [', ', '$city'] },
              ],
            },
            {
              $cond: [
                { $lte: ['$state', ''] },
                '',
                { $concat: [', ', '$state'] },
              ],
            },
            {
              $cond: [{ $lte: ['$zip', ''] }, '', { $concat: [', ', '$zip'] }],
            },
          ],
        },
        addressType: 1,
        payer_id: 1,
        seen: 1,
        customer_user_id: 1,
        invoice_number: 1,
        type: 1,
        total_amount: 1,
        material_subtotal: 1,
        tax_amount: 1,
        material_description: 1,
        note: 1,
        createdAt: 1,
        operation: 1,
        loanApp: 1,
        status: projectInvoiceStatus(builderId),
        company: 1,
        supplier_id: 1,
        createdDate: 1,
        supplierInvitationDetails: 1,
        quote: 1,
        paymentType: '$paymentDetails.paymentType',
        totalPaidAmount: 1,
        totalProcessingAmount: 1,
        totalRemainingAmount: 1,
        lateFee: 1,
      },
    },
  ]

  isActionRequired &&
    pipeline.push({
      $match: {
        $or: [
          {
            $and: [
              { status: { $in: DUE_STATUSES } },
              { totalRemainingAmount: { $gt: 0 } },
            ],
          },
          {
            $and: [
              {
                status: {
                  $in: [
                    invoiceSchemaStatus.placed,
                    invoiceSchemaStatus.authorized,
                  ],
                },
              },
              { type: { $eq: EInvoiceType.QUOTE } },
            ],
          },
        ],
      },
    })

  return Invoice.aggregate(pipeline)
}

export async function getSupplierInvoices(req: Request) {
  let builderId = ''
  let invoice: IInvoice | null | undefined
  let match: PipelineStage | undefined
  const invoiceId = req.query.id
  let supplierId = req.query.companyId
  const invoicesIds = req.query.invoicesIds as any

  if (req.company) {
    builderId = req.company._id.toString()
  }
  if (invoiceId) {
    invoice = await Invoice.findById(invoiceId)
    if (invoice && !supplierId) {
      supplierId = invoice.company_id
    }
  }

  const builderMatch: FilterQuery<any>[] = []

  if (invoicesIds?.length) {
    const objectIdArray = invoicesIds.map(
      (id: string) => new Types.ObjectId(id),
    )
    match = {
      $match: {
        _id: { $in: objectIdArray },
      },
    }
  } else {
    if (builderId) {
      builderMatch.push({ payer_id: builderId })
      const userRoles = await UserRole.aggregate([
        { $match: { company_id: builderId } },
        {
          $lookup: {
            from: User.collection.name,
            as: 'user',
            localField: 'sub',
            foreignField: 'sub',
          },
        },
        { $unwind: '$user' },
        { $sort: { 'user.login': 1 } },
      ])

      const phone_numbers = userRoles
        .map((ur) => [ur.user.login, ur.user.phone])
        .flat()
        .filter(Boolean)
      const emails = userRoles
        .map((ur) => [ur.user.login, ur.user.email])
        .flat()
        .filter(Boolean)

      match = {
        $match: {
          $or: [
            { 'customer.phone': { $in: phone_numbers } },
            { 'customer.email': { $in: emails } },
            { payer_id: builderId },
          ],
        },
      }
    }

    if (!match && invoiceId) {
      if (invoice) {
        if (!supplierId) {
          supplierId = invoice.company_id
        }
        if (invoice.customer_account_id) {
          const customerAccount = await CustomerAccount.findById(
            invoice.customer_account_id,
          )
          if (customerAccount) {
            if (customerAccount.phone) {
              builderMatch.push({ 'customer.phone': customerAccount.phone })
            }
            if (customerAccount.email) {
              builderMatch.push({ 'customer.email': customerAccount.email })
            }

            match = {
              $match: { $or: builderMatch },
            }
          }
        } else {
          match = {
            $match: { _id: invoice._id },
          }
        }
      }
    }
  }

  const pipeline: PipelineStage[] = [
    {
      $lookup: {
        from: CustomerAccount.collection.name,
        as: 'customer',
        let: {
          customer_account_id: {
            $convert: {
              input: '$customer_account_id',
              to: 'objectId',
              onError: null,
            },
          },
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$_id', '$$customer_account_id'],
              },
            },
          },
          {
            $project: {
              bankAccounts: 0,
              house_credit_info: 0,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$customer',
        preserveNullAndEmptyArrays: true,
      },
    },
    match!,
    {
      $match: {
        isDeleted: { $ne: true },
        company_id: { $ne: null },
      },
    },
    { $addFields: { customer_id: { $toString: '$_id' } } },
    {
      $addFields: {
        supplier_id: {
          $cond: {
            if: {
              $or: [
                { $eq: ['$company_id', null] },
                { $eq: ['$company_id', ''] },
              ],
            },
            then: {
              $concat: [
                {
                  $ifNull: [
                    { $trim: { input: '$supplierInvitationDetails.name' } },
                    '',
                  ],
                },
                '_',
                {
                  $ifNull: [
                    { $trim: { input: '$supplierInvitationDetails.email' } },
                    '',
                  ],
                },
              ],
            },
            else: {
              $convert: { input: '$company_id', to: 'objectId', onError: null },
            },
          },
        },
      },
    },
    { $addFields: { createdDate: { $toDate: '$createdAt' } } },
    { $sort: { createdAt: -1 } },
    {
      $lookup: {
        from: Company.collection.name,
        localField: 'supplier_id',
        foreignField: '_id',
        as: 'tempCompany',
        pipeline: [
          {
            $project: {
              bankAccounts: 0,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        company: {
          $cond: {
            if: { $gt: [{ $size: '$tempCompany' }, 0] },
            then: { $arrayElemAt: ['$tempCompany', 0] },
            else: {
              _id: '$supplier_id',
              email: '$supplierInvitationDetails.email',
              name: '$supplierInvitationDetails.name',
              phone: '$supplierInvitationDetails.phone',
              isInvited: true,
            },
          },
        },
      },
    },
    {
      $lookup: {
        from: Operation.collection.name,
        as: 'operation',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
          {
            $match: {
              status: {
                $in: [
                  OPERATION_STATUS.PLACED,
                  OPERATION_STATUS.PROCESSING,
                  OPERATION_STATUS.SUCCESS,
                  OPERATION_STATUS.FAIL,
                ],
              },
              type: OPERATION_TYPES.INVOICE.PAYMENT,
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
        ],
      },
    },
    { $unwind: { path: '$operation', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: LoanApplication.collection.name,
        as: 'loanApp',
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          {
            $addFields: {
              ids: {
                $cond: {
                  if: {
                    $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                  },
                  then: '$invoiceDetails.invoiceId',
                  else: ['$invoiceDetails.invoiceId'],
                },
              },
            },
          },
          {
            $match: {
              $expr: {
                $in: ['$$invoice_id', '$ids'],
              },
            },
          },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
          {
            $project: {
              status: 1,
            },
          },
        ],
      },
    },
    { $unwind: { path: '$loanApp', preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        operationStatus: { $ifNull: ['$operation.status', ''] },
        lateFee: {
          $cond: {
            if: {
              $eq: [
                '$company.settings.arAdvance.isLateInterestChargedToMerchant',
                false,
              ],
            },
            then: {
              $ifNull: ['$paymentDetails.fees', 0],
            },
            else: 0,
          },
        },
      },
    },
    {
      $addFields: {
        totalPaidAmount: {
          $cond: [
            { $ifNull: ['$operation.paidAmount', false] },
            '$operation.paidAmount',
            {
              $cond: [
                { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalProcessingAmount: {
          $cond: [
            { $ifNull: ['$operation.processingAmount', false] },
            '$operation.processingAmount',
            {
              $cond: [
                {
                  $and: [
                    { $eq: ['$operation.status', OPERATION_STATUS.PROCESSING] },
                    { $eq: ['$totalPaidAmount', 0] },
                  ],
                },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalRemainingAmount: {
          $round: [
            {
              $subtract: [
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$operation.amount', '$total_amount'] },
                        { $ifNull: ['$lateFee', 0] },
                      ],
                    },
                    2,
                  ],
                },
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$totalPaidAmount', 0] },
                        { $ifNull: ['$totalProcessingAmount', 0] },
                      ],
                    },
                    2,
                  ],
                },
              ],
            },
            2,
          ],
        },
      },
    },
    {
      $project: {
        _id: 1,
        company_id: 1,
        invoice_document: 1,
        invoice_date: {
          $dateToString: { date: '$invoice_date', format, timezone },
        },
        invoice_due_date: {
          $dateToString: { date: '$invoice_due_date', format, timezone },
        },
        expiration_date: {
          $dateToString: { date: '$expiration_date', format, timezone },
        },
        address: {
          $concat: [
            '$address',
            {
              $cond: [
                { $lte: ['$city', ''] },
                '',
                { $concat: [', ', '$city'] },
              ],
            },
            {
              $cond: [
                { $lte: ['$state', ''] },
                '',
                { $concat: [', ', '$state'] },
              ],
            },
            {
              $cond: [{ $lte: ['$zip', ''] }, '', { $concat: [', ', '$zip'] }],
            },
          ],
        },
        addressType: 1,
        payer_id: 1,
        seen: 1,
        customer_user_id: 1,
        invoice_number: 1,
        type: 1,
        total_amount: 1,
        material_subtotal: 1,
        tax_amount: 1,
        material_description: 1,
        note: 1,
        createdAt: 1,
        operation: 1,
        loanApp: 1,
        status: projectInvoiceStatus(builderId, 'customer'),
        company: 1,
        customer: 1,
        supplier_id: 1,
        createdDate: 1,
        supplierInvitationDetails: 1,
        project_id: 1,
        paymentType: '$paymentDetails.paymentType',
        achDiscountValidUpto: {
          $dateAdd: {
            startDate: '$invoice_date',
            unit: 'day',
            amount: '$company.settings.achDiscount.validityInDays',
          },
        },
        isDue: isDueQuery(),
        totalPaidAmount: 1,
        totalProcessingAmount: 1,
        totalRemainingAmount: 1,
      },
    },
  ]

  if (supplierId) {
    pipeline.push({
      $match: {
        $or: [{ company_id: supplierId }, { supplier_id: supplierId }],
      },
    })
  }
  pipeline.push({
    $addFields: {
      achDiscount: {
        $cond: [
          {
            $and: [
              { $gt: ['$totalRemainingAmount', 0] },
              { $eq: ['$isDue', true] },
              { $in: ['$status', ACHdiscountAllowedStatuses] },
              { $lt: [cstNow, '$achDiscountValidUpto'] },
            ],
          },
          '$company.settings.achDiscount.percentage',
          0,
        ],
      },
    },
  })
  const invoices = await Invoice.aggregate(pipeline)
  const finalResult = invoices.map((inv) => ({
    ...inv,
    achDiscount: (inv.total_amount * inv.achDiscount) / 100,
  }))
  return finalResult
}
