import { dictionaries, ISenderReceiverModel } from '@linqpal/models'
import { attachInvoiceIntegrationConnectorToCompanyConnectorReferences } from './attachInvoiceIntegrationConnectorToCompanyConnectorReferences'
import { Invoice } from '../../models/invoice.model'
import { PaymentNotifications } from './paymentNotifications.service'
import { IPaymentInfo } from './types'
import { IInvoice } from '../../models/types'
import { InvoicePaymentType } from '@linqpal/models'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'
import { isEligibleForIhcCardPayments } from '../payment/achPayment.service'
import { Logger } from '../logger/logger.service'
import mongoose, { Types } from 'mongoose'
import { DEFAULT_TABAPAY_ERROR_MESSAGE } from '../tabapay/helpers'

export interface IInvoiceAfterPaymentActionsParams {
  invoiceIds: string[]
  companyId: string
  userId: string
  invoiceCompanyId: string
  paymentMethod: string
  paymentInfo: IPaymentInfo
  insertNotification: (
    userId: string,
    companyId: string,
    notification: {
      type: dictionaries.NotificationTypes
      content: string
      metadata: any
      receiver: ISenderReceiverModel
    },
  ) => Promise<any>
  makeInvoiceNotificationRead: (params: {
    invoiceId: string
    receiverCompany: string
    alertType: dictionaries.NotificationAlertTypes[]
  }) => Promise<any>
}

export async function handleInvoiceAfterPaymentActions(
  params: IInvoiceAfterPaymentActionsParams,
): Promise<void> {
  const {
    invoiceIds,
    companyId,
    userId,
    invoiceCompanyId,
    paymentMethod,
    paymentInfo,
    insertNotification,
    makeInvoiceNotificationRead,
  } = params

  const invoicesUpdatePromises: Promise<any>[] = []
  const notificationsHandlePromises: Promise<any>[] = []
  const updateNotificationsHandlePromises: Promise<any>[] = []

  await attachInvoiceIntegrationConnectorToCompanyConnectorReferences(
    companyId,
    invoiceIds,
  )
  for (const id of invoiceIds) {
    invoicesUpdatePromises.push(
      Invoice.findOneAndUpdate(
        { _id: id },
        { payer_id: companyId },
        { new: true },
      ).exec(),
    )
    notificationsHandlePromises.push(
      insertNotification(userId, companyId, {
        type: dictionaries.notificationTypes.invoice,
        content: dictionaries.notificationText.invoicePaid,
        metadata: {
          alertType: 'success',
          invoice_id: id,
        },
        receiver: {
          company_id: invoiceCompanyId,
          user_id: '',
        },
      }),
    )
    updateNotificationsHandlePromises.push(
      makeInvoiceNotificationRead({
        invoiceId: id,
        receiverCompany: companyId,
        alertType: [
          'expiring',
          'due',
          'sent',
        ] as dictionaries.NotificationAlertTypes[],
      }),
    )

    if (paymentMethod.toUpperCase() === 'CARD') {
      notificationsHandlePromises.push(
        PaymentNotifications.invoiceProcessingToSupplier(
          id,
          paymentInfo.totalProcessingAmount,
          paymentMethod.toUpperCase(),
        ),
      )
    }
  }

  await Promise.all(invoicesUpdatePromises)
  await Promise.all(notificationsHandlePromises)
  await Promise.all(updateNotificationsHandlePromises)
}

export interface IFactoringValidationResult {
  isValid: boolean
  factoringInvoice?: IInvoice
  errorMessage?: string
}

const logger = new Logger({ module: 'FactoringValidation' })

/**
 * Checks factoring invoice payment rules
 * @param invoices - Array of invoices to validate
 * @param paymentMethod - Payment method being used ('ach' or 'card')
 * @param payerCompanyId - Company ID of the payer
 * @returns Validation result with error message if invalid
 */
export async function checkFactoringInvoices(
  invoices: IInvoice[],
  paymentMethod: string,
  payerCompanyId: string,
): Promise<IFactoringValidationResult> {
  const factoringInvoice = invoices.find(
    (inv) =>
      inv.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
      inv.paymentDetails.arAdvanceStatus === ArAdvanceStatus.Approved,
  )

  if (!factoringInvoice) {
    return { isValid: true }
  }

  if (invoices.length > 1) {
    const errorMessage = `Factoring invoice with id ${factoringInvoice.id} could only be paid separately from the rest`
    logger.error(
      {
        factoringInvoiceId: factoringInvoice.id,
        invoicesCount: invoices.length,
      },
      errorMessage,
    )
    return {
      isValid: false,
      factoringInvoice,
      errorMessage,
    }
  }
  if (paymentMethod !== 'ach') {
    const isCardPaymentAllowed = await isEligibleForIhcCardPayments(
      payerCompanyId,
    )

    if (!isCardPaymentAllowed) {
      const errorMessage = `Factoring invoice with id ${factoringInvoice.id} could only be paid by ach payment method`
      logger.error(
        { factoringInvoiceId: factoringInvoice.id, paymentMethod },
        errorMessage,
      )
      return {
        isValid: false,
        factoringInvoice,
        errorMessage,
      }
    }
  }

  return { isValid: true, factoringInvoice }
}

/**
 * Checks if an invoice is a factoring invoice
 * @param invoice - Invoice to check
 * @returns True if the invoice is a factoring invoice
 */
export function isFactoringInvoice(invoice: IInvoice): boolean {
  return (
    invoice.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
    invoice.paymentDetails.arAdvanceStatus === ArAdvanceStatus.Approved
  )
}

/**
 * Filters invoices to find only factoring invoices
 * @param invoices - Array of invoices to filter
 * @returns Array of factoring invoices only
 */
export function getFactoringInvoices(invoices: IInvoice[]): IInvoice[] {
  return invoices.filter(isFactoringInvoice)
}

/**
 * Sends IHC autopay payment failure notifications
 * @param invoiceIds - Array of invoice IDs
 * @param paymentMethod - Payment method used
 * @param autoDebit - Whether this was an auto debit payment
 * @param requestedAmount - Amount that was requested for payment
 * @param error - The error that occurred
 */
export async function sendIhcAutopayFailureNotifications(
  invoiceIds: string[],
  paymentMethod: string,
  autoDebit: boolean,
  requestedAmount: number | undefined,
  error: unknown,
  paymentAccountId?: string,
): Promise<void> {
  if (!autoDebit || paymentMethod.toUpperCase() !== 'CARD') {
    return
  }

  try {
    const objectIds = invoiceIds.map((id: string) => new Types.ObjectId(id))
    const invoices = await Invoice.find({
      _id: mongoose.trusted({
        $in: objectIds,
      }),
    })

    for (const invoice of invoices) {
      if (isFactoringInvoice(invoice)) {
        let failureReason = 'Unable to process the transaction.'
        if (error instanceof Error && (error as any).tabapayFailureReason) {
          const tabapayReason = (error as any).tabapayFailureReason
          if (tabapayReason !== DEFAULT_TABAPAY_ERROR_MESSAGE) {
            failureReason = tabapayReason
          }
        }

        await PaymentNotifications.ihcAutopayPaymentFailedToCustomer(
          invoice,
          requestedAmount || invoice.total_amount,
          paymentAccountId || '',
          failureReason,
        )

        await PaymentNotifications.ihcAutopayPaymentFailedToOps(
          invoice,
          failureReason,
        )
      }
    }
  } catch (notificationError) {
    logger.error(
      { notificationError, invoiceIds },
      'Failed to send IHC autopay payment failure notifications',
    )
  }
}
