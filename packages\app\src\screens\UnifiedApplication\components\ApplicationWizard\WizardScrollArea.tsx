import React, { FC, useEffect, useRef } from 'react'
import { ScrollView, View } from 'react-native'
import { useResponsive } from '../../../../utils/hooks'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { Steps } from '../../../GeneralApplication/Store/ApplicationSteps'
import { UnifiedApplicationReviewStep } from '../../store/UnifiedApplicationReviewStore'

interface WizardScrollAreaProps {
  children: React.ReactNode
}

export const WizardScrollArea: FC<WizardScrollAreaProps> = ({ children }) => {
  const { screenWidth, sm } = useResponsive()

  const store = useUnifiedApplication()
  const scrollRef = useRef<ScrollView>(null)

  const isPreview =
    store.currentGroup === Steps.review._ &&
    store.reviewStore.currentStep === UnifiedApplicationReviewStep.PREVIEW

  useEffect(() => {
    // reset scroll position when navigating between large components
    // like preview -> coowner -> preview
    scrollRef.current?.scrollTo({ y: 0, animated: false })
  }, [store.currentStep])

  return (
    <ScrollView
      ref={scrollRef}
      contentContainerStyle={{ flexGrow: 1, alignItems: 'center' }}
      showsVerticalScrollIndicator={false}
    >
      <View
        style={{
          paddingHorizontal: sm ? 0 : 20,
          paddingVertical: isPreview ? 0 : 20,
          flex: 1,
          minWidth: screenWidth > 700 ? 700 : '100%',
          maxWidth: screenWidth > 700 ? 700 : undefined,
        }}
      >
        {children}
      </View>
    </ScrollView>
  )
}
