import moment from 'moment'
import { dictionaries, exceptions } from '@linqpal/models'
import { ITransactionResponse } from './types'
import { IOperation } from '../../models/types'
import * as math from 'mathjs'

const TABAPAY_ERROR_MAPPINGS = {
  '54': {
    userMessage: 'Your card has been expired.',
    notificationMessage: 'Expired Card',
  },
  '33': {
    userMessage: 'Your card has been expired.',
    notificationMessage: 'Expired Card',
  },
  '51': {
    userMessage: 'Your account has insufficient funds',
    notificationMessage: 'Insufficient Funds',
  },
  '61': {
    userMessage: 'Withdrawal limit exceeded, use another card',
    notificationMessage: 'Withdrawal limit exceeded',
  },
} as const

export const DEFAULT_TABAPAY_ERROR_MESSAGE =
  'Unable to process the transaction. Please try again.'

function getTabapayErrorInfo(response: ITransactionResponse): {
  userMessage: string
  notificationMessage: string
} | null {
  const networkRC = response.networkRC

  if (
    networkRC &&
    TABAPAY_ERROR_MAPPINGS[networkRC as keyof typeof TABAPAY_ERROR_MAPPINGS]
  ) {
    return TABAPAY_ERROR_MAPPINGS[
      networkRC as keyof typeof TABAPAY_ERROR_MAPPINGS
    ]
  }

  if (response.status !== 'COMPLETED') {
    return {
      userMessage: DEFAULT_TABAPAY_ERROR_MESSAGE,
      notificationMessage: DEFAULT_TABAPAY_ERROR_MESSAGE,
    }
  }

  return null
}

export function handleTabapayReturnedError(response: ITransactionResponse) {
  const errorInfo = getTabapayErrorInfo(response)

  if (errorInfo) {
    console.log(response)
    throw new exceptions.LogicalError(errorInfo.userMessage)
  }

  return true
}

export function getTabapayFailureReason(
  response: ITransactionResponse,
): string | null {
  const errorInfo = getTabapayErrorInfo(response)
  return errorInfo?.notificationMessage || null
}

export function generateTransactionObject({
  operation,
  amount,
  fee,
  reason,
  createTransactionResponse,
  sourceAccountId,
  destinationAccountId,
  isAutoDebit,
}: {
  operation: IOperation
  amount: number
  fee: number
  reason: string | undefined
  createTransactionResponse?: ITransactionResponse
  sourceAccountId: string | undefined
  destinationAccountId: string
  isAutoDebit?: boolean
}) {
  const transactionObject = {
    operation_id: operation.id,
    type: dictionaries.TRANSACTION_TYPES.ACH.TRANSFER,
    payer_id: operation.metadata.payer_id,
    amount: math.round(amount, 2),
    fee: math.round(fee, 2),
    date: moment().format('YYYY-MM-DD HH:mm:ss'),
    payment_method: dictionaries.PAYMENT_METHODS.CARD,
    reason: reason,
    status: dictionaries.TRANSACTION_STATUS.SUCCESS,
    metadata: {
      transactionID: createTransactionResponse?.transactionID,
      transactionNumber: createTransactionResponse?.transactionID,
      transactionType: dictionaries.TABAPAY_TRANSACTION_TYPES.PULL,
      network: createTransactionResponse?.network,
      networkRC: createTransactionResponse?.networkRC,
      networkID: createTransactionResponse?.networkID,
      approvalCode: createTransactionResponse?.approvalCode,
      sourceAccountId: sourceAccountId,
      destinationAccountId: destinationAccountId,
      avsResponseCode: '',
      securityCodeResponseCode: '',
      interchangeFees: '',
      networkFees: '',
      tabapayFees: '',
      cardLastFour: '',
      cardExpirationDate: '',
      isAutoDebit,
    },
  }
  if (createTransactionResponse?.AVS) {
    transactionObject.metadata.avsResponseCode =
      createTransactionResponse?.AVS.codeAVS
    transactionObject.metadata.securityCodeResponseCode =
      createTransactionResponse?.AVS.codeSecurityCode
  }
  if (createTransactionResponse?.fees) {
    transactionObject.metadata.interchangeFees =
      createTransactionResponse?.fees.interchange
    transactionObject.metadata.networkFees =
      createTransactionResponse?.fees.network
    transactionObject.metadata.tabapayFees =
      createTransactionResponse?.fees.tabapay
  }
  if (createTransactionResponse?.card) {
    transactionObject.metadata.cardLastFour =
      createTransactionResponse?.card.last4
    transactionObject.metadata.cardExpirationDate =
      createTransactionResponse?.card.expirationDate
  }

  return transactionObject
}
