import {
  ACH_TRANSACTION_TYPE,
  AwsService,
  connectToDatabase,
  emailService,
  getEnvironmentVariables,
  Invoice,
  invoicesService,
  LedgerService,
  LoanApplication,
  Logger,
  Operation,
  Transaction,
} from '@linqpal/common-backend'
import {
  IOperation,
  ITransaction,
} from '@linqpal/common-backend/src/models/types'
import { dictionaries, exceptions } from '@linqpal/models'
import { S3Event } from 'aws-lambda'
import upperCase from 'lodash/upperCase'
import moment from 'moment-timezone'
import numbro from 'numbro'
import xlsx from 'xlsx'
import { checkAchHeartBeatStatus } from '../linqpal/heartBeat'
import { getPaymentNotificationReceivers } from '@linqpal/common-backend/src/services/invoices.service/notifications'
import { LOAN_APPLICATION_STATUS } from '@linqpal/models/src/dictionaries'
import { handleAchReturn } from '@linqpal/common-backend/src/services/cbw/processAch'
import { findBuilderByAccountId } from '@linqpal/common-backend/src/services/invoices.service/findBuilderAccount'
import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'
import { isFactoringInvoice } from '@linqpal/common-backend/src/services/payment/invoicePostPaymentActions.service'

const {
  OPERATION_STATUS,
  OPERATION_TYPES,
  PAYMENT_METHODS,
  TRANSACTION_STATUS,
} = dictionaries

const logger = new Logger({ module: 'LMS', subModule: 'checkStatus' })

function validateResults<T>(results: PromiseSettledResult<T>[]): T[] {
  const log = logger.startTransaction()
  log.info('checkStatus: Start validating results')
  const rejects = results
    .filter((p) => p.status === 'rejected')
    .map((p) => {
      const { reason } = p as PromiseRejectedResult
      log.error({ reason: reason }, 'checkStatus: Promise rejected')
      return typeof reason === 'string' ? reason : (reason as Error).message
    })
  if (rejects.length > 0) {
    throw new exceptions.BatchError(rejects)
  }
  return results
    .filter((p) => p.status === 'fulfilled')
    .map((p) => (p as PromiseFulfilledResult<T>).value)
}

export async function checkStatus(event: S3Event) {
  const log = logger.startTransaction()
  await getEnvironmentVariables()
  await connectToDatabase()
  log.info('checkStatus: Start checking status')
  const records = event.Records.map(async (record) => {
    log.debug({ record }, 'checkStatus: Start processing record')
    const { key: filename } = record.s3.object

    log.info(`processing CBW report ${filename}`)
    const Body = await AwsService.getS3File(record.s3.bucket.name, filename)
    await checkTransactions(Body)
  })
  const results = await Promise.allSettled(records)
  log.info('checkStatus: Finished checking status')
  return validateResults(results)
}

async function checkTransactions(data: string) {
  const log = logger.startTransaction()
  log.info({ transactions: data }, 'checkStatus: Start checking transactions')
  const wb = xlsx.read(data, { type: 'buffer', raw: true })

  const sheetName = wb.SheetNames[0]
  const firstHeader = wb.Sheets[sheetName].A1.v

  // CBW uploads two identical reports which differs only by headers / structure. To avoid double processing use just one of them
  if (firstHeader !== 'Date') {
    logger.warn('skipping duplicated CBW report')
    return
  }

  log.info({ sheets: wb.Sheets }, 'checkStatus: Read sheets')
  const transactions = xlsx.utils.sheet_to_json<ICBWTransaction>(
    wb.Sheets[sheetName],
    { header: 0, raw: true },
  )
  log.info('checkStatus: Finished checking transactions')
  return validateResults(
    await Promise.allSettled([
      ...transactions.map(checkTransaction),
      ...transactions.map(checkAchHeartBeatStatus),
    ]),
  )
}

interface ICBWTransaction {
  Date: string // Transaction Created Time
  Updated_Date: string // Last Update on the Transaction
  Transaction_Type: string // Transaction Type. Please refer section below for Card specific transaction types
  Transaction_Number: string // Transaction Number Assigned by System of Record authorizing the transaction
  Original_Transaction_Number: string // Contains Reference to the parent transaction number. Mostly present only for transaction type such as fee, reversals/return and dual processing transaction type like completion
  Reference_Number: string // External Reference Number. In case of card transactions, this would Network assigned RRN [Request Reference Number]
  Batch_Id: string // Batch Identifier. Present only for batch processing payments
  Name: string // Debit:  This would contain Receiving Participant Name
  //Credit: This would contain Sending Participant Name
  //E.g: For card transaction, this would contain Merchant Name
  Account_Number: string // Debit: This would contain Receiving Account Number
  //Credit: This would contain Sending Account Number
  //For Card Transaction, this may contain Merchant Terminal Identification Code assigned by Network
  Institution_Name: string // Processing Institution Name
  Insitution_Id: string // Processing Institution ID
  Amount: string // Instructed Amount in $
  Currency_Code: string // Instructed Currency Code
  Status: string // Internal Processing Status
  Transaction_Status: string // External Processing Status of transaction on processing network
  Approvers: string // Approver of the transaction If any
  Reason: string // Reason Description as present in payment instruction
  //For Card Transaction, this would contain cardId
  FiToFiDescription: string // Field indicates who bears the processing charge for that transaction. This would be applicable only for WIRE messages
  Beneficiary: string // Beneficiary Name
  Beneficiary_Account: string // Beneficiary Account Number
  //This may be present when actual beneficiary is different from Receiving account. E.g. correspondent banking
  Return_Code: string // Return reason code if applicable
}

async function checkTransaction(item: ICBWTransaction) {
  const log = logger.startTransaction()
  try {
    log.info({ transaction: item }, 'checkStatus: Start checking transaction')
    const { Transaction_Status = 'PENDING', Transaction_Type } = item
    switch (Transaction_Type) {
      case 'ACH_PULL_RETURN':
      case 'ACH_PULL_REVERSAL':
        await processAchReturn(item)
        break
      case 'ACH_RETURN':
      case 'ACH_REVERSAL':
        await processAchOutReturn(item)
        break
      case 'GL_TRANSFER':
      case 'ACH_PULL':
      case 'ACH_OUT':
        if (Transaction_Status === 'PENDING') {
          log.debug(
            { transaction: item },
            'checkStatus: Transaction is pending. Skip it',
          )
          return
        }
        await processAch(item)
        break
      default:
        log.info({ transaction: item }, 'checkStatus: Transaction was ignored')
    }
  } catch (e) {
    log.error({ error: e }, 'checkStatus: Error while checking transaction')
  }
  log.info({ transaction: item }, 'checkStatus: Finished checking transaction')
}

async function processAchReturn(item: ICBWTransaction) {
  const log = logger.startTransaction()
  log.info({ transaction: item }, 'checkStatus: Started processing ACH return')
  const { Original_Transaction_Number, Transaction_Number, Transaction_Type } =
    item
  await handleAchReturn(
    Original_Transaction_Number,
    Transaction_Number,
    Transaction_Type,
  )
  log.info({ transaction: item }, 'checkStatus: Finished processing ACH return')
}

async function processAchOutReturn(item: ICBWTransaction) {
  const log = logger.startTransaction()
  log.info(
    { transaction: item },
    'checkStatus: Start processing ACH OUT return',
  )
  const { Original_Transaction_Number } = item
  const transactions = await Transaction.find({
    'metadata.transactionNumber': Original_Transaction_Number,
  })
  if (transactions.length) {
    for (const transaction of transactions) {
      const op = await Operation.findById(transaction.operation_id)
      if (op) {
        transaction.status = TRANSACTION_STATUS.ERROR
        await transaction.save()

        const invoice = await Invoice.findById(op.owner_id)
        if (invoice && !isFactoringInvoice(invoice)) {
          await invoicesService.sendInvoicePaymentNotification(
            op.owner_id,
            op.status,
          )
        }
        log.info(
          { transaction: item },
          'checkStatus: End processing ACH return',
        )
      }
      await Promise.all([
        succeedOperation(transaction.operation_id),
        failOperation(transaction.operation_id),
        operationProcessing(transaction.operation_id),
      ])
    }
  }
}

async function processAch(item: ICBWTransaction) {
  const log = logger.startTransaction()
  log.info({ transaction: item }, 'checkStatus: Start processing ACH')
  const { Transaction_Status, Transaction_Number, Transaction_Type } = item
  if (!Transaction_Number) return
  const transactions = await Transaction.find({
    'metadata.transactionNumber': Transaction_Number,
  })
  for (const transaction of transactions) {
    if (
      !transaction ||
      [TRANSACTION_STATUS.ERROR, TRANSACTION_STATUS.CANCELED].includes(
        transaction.status,
      )
    )
      continue
    const op = await Operation.findById(transaction.operation_id)
    if (!op) continue

    const isTransactionAlreadySuccess =
      transaction.status === TRANSACTION_STATUS.SUCCESS

    if (Transaction_Status === 'PROCESSED') {
      transaction.status = TRANSACTION_STATUS.SUCCESS
      if (Transaction_Type === 'ACH_PULL') {
        if (!op.metadata) op.metadata = {}
        if (!op.metadata.pullResult) {
          op.metadata.pullResult = moment().toDate()
          op.markModified('metadata')
        }
      } else {
        op.status = OPERATION_STATUS.SUCCESS

        // because of operation status update to SUCCESS
        // operationCheck scheduled function won't process this operation
        // and transaction.date won't be set - so force set it here
        transaction.date = moment()

        if (!isTransactionAlreadySuccess) {
          switch (op.type) {
            case OPERATION_TYPES.INVOICE.PAYMENT:
            case OPERATION_TYPES.INVOICE.FINAL_PAYMENT:
              await notifySupplier(op, transaction) // This one should be reimplemented, due to no CBW anymore.
              break
          }
        }
      }
    } else {
      transaction.status = TRANSACTION_STATUS.ERROR
      transaction.metadata.error = item
      transaction.markModified('metadata')
    }
    await transaction.save()

    if (Transaction_Type === 'ACH_PULL' && !isTransactionAlreadySuccess) {
      await LedgerService.handleAchPullSuccess(transaction)
    }

    if (Transaction_Type === 'ACH_OUT' && !isTransactionAlreadySuccess) {
      await LedgerService.handleAchOutSuccess(transaction)
    }

    await op.save()

    await Promise.all([
      succeedOperation(transaction.operation_id),
      failOperation(transaction.operation_id),
      operationProcessing(transaction.operation_id),
    ])

    if (!isTransactionAlreadySuccess) {
      const invoice = await Invoice.findById(op.owner_id)
      if (invoice && !isFactoringInvoice(invoice)) {
        await invoicesService.sendInvoicePaymentNotification(
          op.owner_id,
          op.status,
        )
      }
    }

    log.info({ transaction: item }, 'checkStatus: Finished processing ACH')
  }
}

async function succeedOperation(operation_id: string) {
  const [pull_transactions, out_transactions] = await Promise.all([
    Transaction.find({
      operation_id,
      status: TRANSACTION_STATUS.SUCCESS,
      'metadata.transactionType': ACH_TRANSACTION_TYPE.PULL,
    }),
    Transaction.find({
      operation_id,
      status: TRANSACTION_STATUS.SUCCESS,
      'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
    }),
  ])

  if (pull_transactions.length > 0 && out_transactions.length > 0) {
    const operation = await Operation.findById(operation_id)
    operation && (operation.status = OPERATION_STATUS.SUCCESS)
    await operation?.save()

    if (
      operation &&
      operation.metadata.payment_method === 'card' &&
      operation.type === 'invoice_payment'
    ) {
      const outTransaction = out_transactions[0]

      await AwsService.sendSQSMessage(
        'netsuite-connector',
        JSON.stringify({
          id: operation.owner_id,
          operationType: 'InvoiceStatusUpdate',
          status: 'Paid',
        }),
        'NETSUITE',
      )

      await AwsService.sendSQSMessage(
        'netsuite-connector',
        JSON.stringify({
          id: operation.owner_id,
          operationType: 'InvoicePaid',
          status: 'final',
          paymentMethod: 'card',
          amount: outTransaction.amount,
          fee: outTransaction.fee ?? 0,
        }),
        'NETSUITE',
      )
    }
  }
}

async function failOperation(operation_id: string) {
  const [error_transactions, processing_transactions, success_transactions] =
    await Promise.all([
      Transaction.find({
        operation_id,
        status: TRANSACTION_STATUS.ERROR,
        'metadata.transactionType': ACH_TRANSACTION_TYPE.PULL,
      }),
      Transaction.find({
        operation_id,
        status: TRANSACTION_STATUS.PROCESSING,
        'metadata.transactionType': ACH_TRANSACTION_TYPE.PULL,
      }),
      Transaction.find({
        operation_id,
        status: TRANSACTION_STATUS.SUCCESS,
        'metadata.transactionType': ACH_TRANSACTION_TYPE.PULL,
      }),
    ])

  if (
    error_transactions.length > 0 &&
    processing_transactions.length === 0 &&
    success_transactions.length === 0
  ) {
    const operation = await Operation.findById(operation_id)
    operation && (operation.status = OPERATION_STATUS.FAIL)
    await operation?.save()
  }
}

async function operationProcessing(operation_id: string) {
  const [pull_transactions, out_transactions] = await Promise.all([
    Transaction.find({
      operation_id,
      status: TRANSACTION_STATUS.SUCCESS,
      'metadata.transactionType': ACH_TRANSACTION_TYPE.PULL,
    }),
    Transaction.find({
      operation_id,
      status: TRANSACTION_STATUS.SUCCESS,
      'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
    }),
  ])

  if (pull_transactions.length > 0 && out_transactions.length === 0) {
    const operation = await Operation.findById(operation_id)
    operation && (operation.status = OPERATION_STATUS.PROCESSING)
    await operation?.save()
  }
}

async function notifySupplier(op: IOperation, transaction: ITransaction) {
  const invoice = await Invoice.findById(op.owner_id)
  const { payment_method } = op.metadata
  if (!invoice || !invoice.customer_account_id) return

  if (isFactoringInvoice(invoice)) return

  const [customer, emails, loanApplication] = await Promise.all([
    findBuilderByAccountId(invoice.customer_account_id),
    getPaymentNotificationReceivers(invoice),
    LoanApplication.findOne({
      'invoiceDetails.invoiceId': {
        $in: [invoice.id],
        status: LOAN_APPLICATION_STATUS.APPROVED,
      },
    }),
  ])

  const customerName = CompanyUtils.getCompanyName(customer)
  const invoiceNumber = invoice.invoice_number

  const amount = numbro(invoice.total_amount).formatCurrency({
    currencySymbol: '$',
    thousandSeparated: true,
    mantissa: 2,
  })
  const amountProcessed = numbro(transaction.amount).formatCurrency({
    currencySymbol: '$',
    thousandSeparated: true,
    mantissa: 2,
  })

  let labelProcessed = 'Amount processed'
  let subject = `Invoice ${invoice.invoice_number} is processed for ${amountProcessed}`
  let paymentType = upperCase(payment_method)

  if (payment_method === PAYMENT_METHODS.LOAN) {
    paymentType = loanApplication?.metadata?.repayment?.autoTradeCreditEnabled
      ? 'Automated Trade Credit'
      : 'BlueTape Credit'

    switch (op.type) {
      case OPERATION_TYPES.INVOICE.PAYMENT:
        labelProcessed = 'Advance payment'
        subject = `Advance payment is processed for ${amountProcessed}`
        break
      case OPERATION_TYPES.INVOICE.FINAL_PAYMENT:
        labelProcessed = 'Final payment'
        subject = `Final payment is processed for ${amountProcessed}`
        break
    }
  }

  await Promise.all(
    emails.map((to) => {
      const payload = {
        subject,
        customerName,
        invoiceNumber,
        amount,
        paymentType,
        labelProcessed,
        amountProcessed,
        transactionNumber: transaction.metadata.transactionNumber,
      }

      logger.info(
        `sending supplier notification to ${to}: ${JSON.stringify(payload)}`,
      )

      emailService.send({
        to,
        templateId: emailService.TEMPLATES.INVOICE_PROCESSED,
        dynamicTemplateData: payload,
      })
    }),
  )
}
