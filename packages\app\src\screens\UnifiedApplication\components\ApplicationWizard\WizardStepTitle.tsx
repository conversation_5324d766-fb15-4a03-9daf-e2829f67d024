import React, { FC } from 'react'
import { useResponsive } from '../../../../utils/hooks'
import { useTranslation } from 'react-i18next'
import { StyleSheet, View } from 'react-native'
import { BtPlainText } from '@linqpal/components/src/ui'
import { IApplicationStepEditorTitle } from '../../flow/ApplicationEditorSelector'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { observer } from 'mobx-react'

export const WizardStepTitle: FC = observer(() => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  // TODO: VK: Unified: remove cast to any, it's just for backward compatibility during refactoring
  const title = store.editorOptions?.title as IApplicationStepEditorTitle

  const textSource = title?.text
  if (!textSource) return null

  const text = textSource instanceof Function ? textSource() : textSource

  return (
    <View style={title?.style}>
      <BtPlainText
        style={[styles.title, sm ? styles.titleDesktop : styles.titleMobile]}
      >
        {t(text as any)}
      </BtPlainText>
    </View>
  )
})

const styles = StyleSheet.create({
  title: {
    fontWeight: '600',
    color: '#003353',
    marginBottom: 10,
  },
  titleDesktop: {
    fontSize: 24,
    lineHeight: 36,
  },
  titleMobile: {
    fontSize: 18,
    lineHeight: 26,
  },
})
