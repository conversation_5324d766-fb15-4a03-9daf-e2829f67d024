import SgMail from '@sendgrid/mail'
import { AttachmentData } from '@sendgrid/helpers/classes/attachment'
import moment, { Moment } from 'moment-timezone'
import { toCurrency } from '@linqpal/models/src/helpers/formatter'
import SmsNotifications from './SmsNotifications'
import { InvoicePaymentType } from '@linqpal/models'

export type EmailNotification = Omit<SgMail.MailDataRequired, 'from'>

//prettier-ignore
const TEMPLATES = {
  INVOICE_PROCESSING: 'd-626f3e1e802743098020731196e61433',

  DRAW_AUTO_PAYMENT_UPCOMING_FOR_SINGLE_INVOICE: 'd-350409bc97864f78b14cbf853bc541f9',
  DRAW_AUTO_PAYMENT_UPCOMING_FOR_GROUPED_INVOICES: 'd-0fa7bb79794648d6a5636878b0ecd421',
  DRAW_MANUAL_PAYMENT_UPCOMING_FOR_SINGLE_INVOICE: 'd-aee53bddf3e747a7bc51a66e5fb312c0',
  DRAW_MANUAL_PAYMENT_UPCOMING_FOR_GROUPED_INVOICES: 'd-c7afec31e9484ab3af438c720cde7ddd',
  DRAW_APPROVED_FOR_SINGLE_INVOICE_SINGLE_PAYMENT: 'd-fc79ff8139e14f8f98c1b210e8fbc0e7',
  DRAW_APPROVED_FOR_SINGLE_INVOICE_MULTIPLE_PAYMENTS: 'd-f597db36335340c9a7fe6a0b097d1c6b',
  DRAW_APPROVED_FOR_GROUPED_INVOICE_SINGLE_PAYMENT: 'd-a1c0ecfd92c7495586f311dd052be7ac',
  DRAW_APPROVED_FOR_GROUPED_INVOICE_MULTIPLE_PAYMENTS: 'd-391b35873b1449c58027ed67fbaeb254',
  DRAW_PAID_OFF_AND_CLOSED_FOR_SINGLE_INVOICE: 'd-f0a6d058b0294f8c98ff59b84c2fcb99',
  DRAW_PAID_OFF_AND_CLOSED_FOR_GROUPED_INVOICES: 'd-f14c68ce368c471d8c757765cb60b575',

  AUTO_TRADE_CREDIT_APPROVED: 'd-02db4d3d488a491c800aad881d7a6241',
  AUTO_TRADE_CREDIT_APPROVED_TO_SUPPLIER: 'd-65df9c96743b40f6a1ccb488a6313dc8',
  AUTO_TRADE_CREDIT_CANCELED_ON_DISPUTE: 'd-7b644905081041eebee727480d2667aa',
  AUTO_TRADE_CREDIT_CANCELED_BEFORE_APPROVAL: 'd-dc9259fa43ee4721a4b35cd1110a506d',
  AUTO_TRADE_CREDIT_REJECTED: 'd-bf1edd06c27e48588d455eb591c4b2d1',

  QUOTE_CREATED: 'd-4cfe5e9e5e0d49228f8c9323153bd3bd',
  QUOTE_AUTHORIZED: 'd-74a84543da844758887dba70ca3b858a',
  QUOTE_AUTHORIZED_TO_SUPPLIER: 'd-d1630aae62704763adc9602f84a06b24',
  QUOTE_AUTO_AUTHORIZED_TO_OPS_TEAM: 'd-4744133862c64a23a868eed085065077',
  QUOTE_DRAW_CREATED: 'd-b80cc712706242d0ac6f4f9b558dd53c',
  QUOTE_CANCELED: 'd-f9ea7fd88d4e4d25a2d6ee137c470a7b',
  QUOTE_CANCELED_TO_SUPPLIER: 'd-9099b5385f00450390e7d022fac2fa5a',
  QUOTE_REJECTED: 'd-6a282803ad634db9af0d914d4a509be1',
  QUOTE_REJECTED_TO_SUPPLIER: 'd-6a282803ad634db9af0d914d4a509be1',
  QUOTE_AUTHORIZATION_EXTENDED: 'd-7aef74b188594059b2d0d951ee1e3d22',
  QUOTE_AUTHORIZATION_EXTENDED_TO_SUPPLIER:
    'd-b8982ee67708410faa78f89100f36f56',
  QUOTE_AUTHORIZATION_NEARING_EXPIRATION_TO_SUPPLIER:
    'd-449cf301104a42a4b9a4ca0e7940bb92',
  QUOTE_AUTHORIZATION_EXPIRED: 'd-79f9c5901c2043aebe2d63cfe53cc908',
  QUOTE_AUTHORIZATION_EXPIRED_TO_SUPPLIER: 'd-58227df8ffb0498f89352469a14fa82b',

  IHC_QUOTE_AUTHORIZED_TO_SUPPLIER: 'd-26bcb816753748bca660b1c77007dfb7',
  IHC_QUOTE_NEARING_EXPIRATION_TO_SUPPLIER:
    'd-4b137ac100cf46e0ab98cb5bd54baa9d',
  IHC_QUOTE_EXPIRED_TO_SUPPLIER: 'd-8d1d95753dc348438135babe37be7f77',
  IHC_QUOTE_REJECTED_TO_SUPPLIER: 'd-e92fdba2ca4a4160a4c5a9679605a462',
  IHC_QUOTE_CANCELED_TO_SUPPLIER: 'd-723d5f3b1b274ccbb0c272a842e43928',

  NEW_CUSTOMER_INVITATION_FACTORING: 'd-8802ab94024f446387adabf7dd3a054e',
  NEW_CUSTOMER_INVITATION: 'd-cab4fd7435e14f7f9d236dc1f8d9c106',
  NEW_USER_INVITATION: 'd-31608db497a34481862fb90d8138ebe8',

  INVOICE_APPROVED_FOR_FACTORING: 'd-0b7d9271f5664a8f8af6dd7059150d63',
  INVOICE_REJECTED_FOR_FACTORING: 'd-f316d9c33da541ddb2d8cc754b65da02',

  INVOICE_PROCESSING_TO_SUPPLIER: 'd-851580e722f74d5b8e32a4b45e89d3d1',
  INVOICE_PROCESSING_TO_CUSTOMER: 'd-9003686030714ab79b3b490fcc107cf5',
  INVOICE_PROCESSING_TO_GUEST_PAYER: 'd-4a8d4cba60504bfa8306ad5be671b1cf',

  IHC_AUTOPAY_PAYMENT_PROCESSING: 'd-c6427332b69c498a9986d9ed4a2c5f7f',
  IHC_AUTOPAY_PAYMENT_FAILED_CUSTOMER: 'd-8bf392ec15104355b12261daa2a44c6c',
  IHC_AUTOPAY_PAYMENT_FAILED_OPS: 'd-5f10aa85569f408bbce4cbb3ef9d850c',

  GET_PAID_AGREEMENT_APPROVAL_REQUEST: 'd-b2904794868b4c7ba8ad76162e323ee3',
}

export default class EmailNotifications {
  // region Regular draws
  static drawApprovedForSingleInvoiceSinglePayment(params: {
    customerName: string
    supplierName: string
    invoiceNumber: string
    invoiceAmount: number
    drawAmount: number
    paymentDate: Moment
  }): EmailNotification {
    return {
      templateId: TEMPLATES.DRAW_APPROVED_FOR_SINGLE_INVOICE_SINGLE_PAYMENT,
      dynamicTemplateData: {
        ...params,
        invoiceAmount: toCurrency(params.invoiceAmount),
        drawAmount: toCurrency(params.drawAmount),
        paymentDate: params.paymentDate.format('MM/DD/YYYY'),
      },
    }
  }

  static drawApprovedForSingleInvoice(params: {
    customerName: string
    supplierName: string
    invoiceNumber: string
    invoiceAmount: number
    drawAmount: number
    payments: { dueDate: Moment }[]
  }): EmailNotification {
    return params.payments.length === 1
      ? {
          templateId: TEMPLATES.DRAW_APPROVED_FOR_SINGLE_INVOICE_SINGLE_PAYMENT,
          dynamicTemplateData: {
            ...params,
            invoiceAmount: toCurrency(params.invoiceAmount),
            drawAmount: toCurrency(params.drawAmount),
            paymentDate: params.payments[0].dueDate.format('MM/DD/YYYY'),
          },
        }
      : {
          templateId:
            TEMPLATES.DRAW_APPROVED_FOR_SINGLE_INVOICE_MULTIPLE_PAYMENTS,
          dynamicTemplateData: {
            ...params,
            invoiceAmount: toCurrency(params.invoiceAmount),
            drawAmount: toCurrency(params.drawAmount),
            payments: params.payments.map((payment, index) => ({
              number: this.formatIndex(index + 1),
              dueDate: payment.dueDate.format('MM/DD/YYYY'),
            })),
          },
        }
  }

  static drawApprovedForGroupedInvoices(params: {
    customerName: string
    supplierName: string
    totalInvoiceAmount: number
    drawAmount: number
    invoices: { number: string; amount: number }[]
    payments: { dueDate: Moment }[]
  }): EmailNotification {
    return params.payments.length === 1
      ? {
          templateId:
            TEMPLATES.DRAW_APPROVED_FOR_GROUPED_INVOICE_SINGLE_PAYMENT,
          dynamicTemplateData: {
            ...params,
            totalInvoiceAmount: toCurrency(params.totalInvoiceAmount),
            drawAmount: toCurrency(params.drawAmount),
            paymentDate: params.payments[0].dueDate.format('MM/DD/YYYY'),
            invoices: params.invoices.map((i) => ({
              number: i.number,
              amount: toCurrency(i.amount),
            })),
          },
        }
      : {
          templateId:
            TEMPLATES.DRAW_APPROVED_FOR_GROUPED_INVOICE_MULTIPLE_PAYMENTS,
          dynamicTemplateData: {
            ...params,
            totalInvoiceAmount: toCurrency(params.totalInvoiceAmount),
            drawAmount: toCurrency(params.drawAmount),
            paymentDate: params.payments[0].dueDate.format('MM/DD/YYYY'),
            invoices: params.invoices.map((i) => ({
              number: i.number,
              amount: toCurrency(i.amount),
            })),
            payments: params.payments.map((payment, index) => ({
              number: this.formatIndex(index + 1),
              dueDate: payment.dueDate.format('MM/DD/YYYY'),
            })),
          },
        }
  }

  static drawPaymentUpcoming(
    paymentType: 'auto' | 'manual',
    params: {
      supplierName: string
      paymentAmount: number
      paymentDate: Moment
      dueDays: number
      invoices: { number: string }[]
    },
  ): EmailNotification {
    const dueDays = {
      3: 'in 3 days',
      2: 'in 2 days',
      1: 'in 1 day',
      0: 'today',
    }[params.dueDays]

    const paymentAmount = toCurrency(params.paymentAmount)
    const paymentDate = params.paymentDate.format('MM/DD/YYYY')

    return params.invoices.length === 1
      ? {
          templateId:
            paymentType === 'auto'
              ? TEMPLATES.DRAW_AUTO_PAYMENT_UPCOMING_FOR_SINGLE_INVOICE
              : TEMPLATES.DRAW_MANUAL_PAYMENT_UPCOMING_FOR_SINGLE_INVOICE,
          dynamicTemplateData: {
            ...params,
            paymentAmount,
            paymentDate,
            dueDays,
            invoiceNumber: params.invoices[0].number,
          },
        }
      : {
          templateId:
            paymentType === 'auto'
              ? TEMPLATES.DRAW_AUTO_PAYMENT_UPCOMING_FOR_GROUPED_INVOICES
              : TEMPLATES.DRAW_MANUAL_PAYMENT_UPCOMING_FOR_GROUPED_INVOICES,
          dynamicTemplateData: {
            ...params,
            paymentAmount,
            paymentDate,
            dueDays,
          },
        }
  }

  static drawPaidOffAndClosedForSingleInvoice(params: {
    supplierName: string
    invoiceNumber: string
    drawId: string
    totalPaid: number
    datePaidOff: Moment
  }): EmailNotification {
    return {
      templateId: TEMPLATES.DRAW_PAID_OFF_AND_CLOSED_FOR_SINGLE_INVOICE,
      dynamicTemplateData: {
        ...params,
        totalPaid: toCurrency(params.totalPaid),
        datePaidOff: params.datePaidOff.format('MM/DD/YYYY'),
      },
    }
  }

  static drawPaidOffAndClosedForGroupedInvoices(params: {
    supplierName: string
    invoiceNumbers: string[]
    drawId: string
    totalPaid: number
    datePaidOff: Moment
  }): EmailNotification {
    return {
      templateId: TEMPLATES.DRAW_PAID_OFF_AND_CLOSED_FOR_GROUPED_INVOICES,
      dynamicTemplateData: {
        ...params,
        totalPaid: toCurrency(params.totalPaid),
        datePaidOff: params.datePaidOff.format('MM/DD/YYYY'),
      },
    }
  }
  // endregion Regular draws

  // region Auto Trade Credit
  static autoTradeCreditSubmittedToUW(params: {
    customerName: string
    supplierName: string
    invoiceNumber: string
    invoiceAmount: number
    invoiceDueDate: string
    attachment?: AttachmentData
  }): EmailNotification {
    return {
      subject: `Automated Trade Credit: ${params.customerName}`,
      html: `<strong>Supplier Name:</strong> ${params.supplierName} <br/>
             <strong>Customer Name:</strong> ${params.customerName} <br/>
             <strong>Invoice Number:</strong> ${params.invoiceNumber} <br/>
             <strong>Invoice Amount:</strong> ${toCurrency(
               params.invoiceAmount,
             )} <br/>
             <strong>Invoice Due Date:</strong> ${params.invoiceDueDate} <br/>`,
      attachments: params.attachment ? [params.attachment] : undefined,
    }
  }

  static autoTradeCreditApproved(params: {
    customerName: string
    supplierName: string
    invoiceAmount: number
    invoiceNumber: string
    drawAmount: number
    repaymentDate: Moment | null
    loginUrl: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.AUTO_TRADE_CREDIT_APPROVED,
      dynamicTemplateData: {
        ...params,
        url: params.loginUrl,
        invoiceAmount: toCurrency(params.invoiceAmount),
        drawAmount: toCurrency(params.drawAmount),
        repaymentDate: params.repaymentDate
          ? params.repaymentDate.format('MM/DD/YYYY')
          : 'n/a',
      },
    }
  }

  static autoTradeCreditApprovedToSupplier(params: {
    customerCompany: string
    invoiceNumber: string
    invoiceAmount: number
    advanceAmount: number
    advanceDisbursementDate: Moment
    finalAmount: number
    finalDisbursementDate: Moment
  }): EmailNotification {
    return {
      templateId: TEMPLATES.AUTO_TRADE_CREDIT_APPROVED_TO_SUPPLIER,
      dynamicTemplateData: {
        ...params,
        invoiceAmount: toCurrency(params.invoiceAmount),
        advanceAmount: toCurrency(params.advanceAmount),
        advanceDisbursementDate:
          params.advanceDisbursementDate.format('MM/DD/YYYY'),
        finalAmount: toCurrency(params.finalAmount),
        finalDisbursementDate:
          params.finalDisbursementDate.format('MM/DD/YYYY'),
      },
    }
  }

  static autoTradeCreditCanceledOnDispute(params: {
    customerName: string
    supplierName: string
    invoiceNumber: string
    drawAmount: number
  }): EmailNotification {
    return {
      templateId: TEMPLATES.AUTO_TRADE_CREDIT_CANCELED_ON_DISPUTE,
      dynamicTemplateData: {
        ...params,
        drawAmount: toCurrency(params.drawAmount),
      },
    }
  }

  static autoTradeCreditCanceledBeforeApproval(params: {
    drawAmount: number
    invoiceNumber: string
    customerName: string
    supplierName: string
    url: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.AUTO_TRADE_CREDIT_CANCELED_BEFORE_APPROVAL,
      dynamicTemplateData: {
        ...params,
        drawAmount: toCurrency(params.drawAmount),
      },
    }
  }

  static autoTradeCreditRejected(params: {
    customerName: string
    supplierName: string
    invoiceNumber: string
    invoiceLink: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.AUTO_TRADE_CREDIT_REJECTED,
      dynamicTemplateData: params,
    }
  }
  // endregion Auto Trade Credit

  // region Loan Application
  static loanApplicationSubmittedToUW(params: {
    supplierName: string
    customerName: string
    applicationUrl: string
  }): EmailNotification {
    return {
      subject: 'Supplier customer submitted an invoice for a credit',
      html: `<div>
                Supplier customer submitted an invoice for a credit<br>
                Supplier name: <b>${params.supplierName}</b><br>
                Customer name: <b>${params.customerName}</b><br>
                <a href='${params.applicationUrl}'>Show application</a>
              </div>`,
    }
  }

  static loanApplicationCanceled(params: {
    customerName: string
    url: string
  }): EmailNotification {
    return {
      subject: 'Credit request notification',
      html: this.paragraph(SmsNotifications.loanApplicationCanceled(params)),
    }
  }

  static loanApplicationRejected(params: {
    loginUrl: string
  }): EmailNotification {
    return {
      subject: 'Credit request notification',
      html: this.paragraph(
        SmsNotifications.loanApplicationRejected({ loginUrl: params.loginUrl }),
      ),
    }
  }
  // endregion Loan

  // region Prequal
  static prequalCanceled(params: {
    customerName: string
    url: string
  }): EmailNotification {
    return {
      subject: 'Credit request notification',
      html: this.paragraph(SmsNotifications.prequalCanceled(params)),
    }
  }

  static prequalRejected(): EmailNotification {
    return {
      subject: 'Credit request notification',
      html: this.paragraph(SmsNotifications.prequalRejected()),
    }
  }
  // endregion Prequal

  //region Quote
  static quoteCreated(params: {
    customerName: string
    supplierName: string
    quoteAmount: number
    quoteLink: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.QUOTE_CREATED,
      dynamicTemplateData: {
        ...params,
        quoteAmount: toCurrency(params.quoteAmount),
      },
    }
  }

  static quoteApplicationSubmittedToUW(params: {
    customerName: string
    supplierName: string
    quoteNumber: string
    quoteAmount: number
    attachment?: AttachmentData
    isAutoTradeCredit: boolean
  }): EmailNotification {
    // prettier-ignore
    return {
      subject: params.isAutoTradeCredit
        ? `Auto Trade Credit - Quote: ${params.customerName}`
        : `Loan Application - Quote: ${params.customerName}`,
      html: `<strong>Supplier Name:</strong> ${params.supplierName} <br/>
             <strong>Customer Name:</strong> ${params.customerName} <br/>
             <strong>Quote Number:</strong> ${params.quoteNumber} <br/>
             <strong>Quote Amount:</strong> ${toCurrency(params.quoteAmount)} <br/>`,
      attachments: params.attachment ? [params.attachment] : undefined,
    }
  }

  static quoteAuthorizedToOpsTeam(params: {
    supplier: string
    customer: string
    quoteNumber: string
    quoteAmount: number
    quoteExpiration: Date
  }): EmailNotification {
    return {
      templateId: TEMPLATES.QUOTE_AUTO_AUTHORIZED_TO_OPS_TEAM,
      dynamicTemplateData: {
        ...params,
        quoteAmount: toCurrency(params.quoteAmount),
        quoteExpiration: moment(params.quoteExpiration).format('MM/DD/YYYY'),
      },
    }
  }

  static quoteAuthorized(params: {
    customerName: string
    supplierName: string
    quoteNumber: string
    quoteAmount: number
    drawAmount: number
    expirationDays: number
  }): EmailNotification {
    return {
      templateId: TEMPLATES.QUOTE_AUTHORIZED,
      dynamicTemplateData: {
        ...params,
        quoteAmount: toCurrency(params.quoteAmount),
        drawAmount: toCurrency(params.drawAmount),
      },
    }
  }

  static quoteAuthorizedToSupplier(
    paymentType: InvoicePaymentType | null,
    params: {
      customerName: string
      supplierName: string
      quoteNumber: string
      expirationDays: number
    },
  ): EmailNotification {
    return {
      templateId:
        paymentType === InvoicePaymentType.FACTORING
          ? TEMPLATES.IHC_QUOTE_AUTHORIZED_TO_SUPPLIER
          : TEMPLATES.QUOTE_AUTHORIZED_TO_SUPPLIER,
      dynamicTemplateData: params,
    }
  }

  static quoteDrawCreated(params: {
    customerName: string
    supplierName: string
    invoiceNumber: string
    invoiceAmount: number
    drawAmount: number
    repaymentDate: Moment | null
    loginUrl: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.QUOTE_DRAW_CREATED,
      dynamicTemplateData: {
        ...params,
        invoiceAmount: toCurrency(params.invoiceAmount),
        drawAmount: toCurrency(params.drawAmount),
        repaymentDate: params.repaymentDate
          ? params.repaymentDate.format('MM/DD/YYYY')
          : 'n/a',
      },
    }
  }

  static quoteCanceled(params: {
    customerName: string
    supplierName: string
    quoteNumber: string
  }) {
    return {
      templateId: TEMPLATES.QUOTE_CANCELED,
      dynamicTemplateData: params,
    }
  }

  static quoteCanceledToSupplier(
    paymentType: InvoicePaymentType | null,
    params: {
      customerName: string
      supplierName: string
      quoteNumber: string
    },
  ) {
    return {
      templateId:
        paymentType === InvoicePaymentType.FACTORING
          ? TEMPLATES.IHC_QUOTE_CANCELED_TO_SUPPLIER
          : TEMPLATES.QUOTE_CANCELED_TO_SUPPLIER,
      dynamicTemplateData: params,
    }
  }

  static quoteRejected(params: {
    customerName: string
    supplierName: string
    quoteNumber: string
  }) {
    return {
      templateId: TEMPLATES.QUOTE_REJECTED,
      dynamicTemplateData: params,
    }
  }

  static quoteRejectedToSupplier(
    paymentType: InvoicePaymentType | null,
    params: {
      customerName: string
      supplierName: string
      quoteNumber: string
    },
  ): EmailNotification {
    return {
      templateId:
        paymentType === InvoicePaymentType.FACTORING
          ? TEMPLATES.IHC_QUOTE_REJECTED_TO_SUPPLIER
          : TEMPLATES.QUOTE_REJECTED_TO_SUPPLIER,
      dynamicTemplateData: params,
    }
  }

  static quoteAuthorizationExtended(params: {
    customerName: string
    supplierName: string
    quoteNumber: string
    authorizationDeadline: Moment
  }): EmailNotification {
    return {
      templateId: TEMPLATES.QUOTE_AUTHORIZATION_EXTENDED,
      dynamicTemplateData: {
        ...params,
        authorizationDeadline:
          params.authorizationDeadline.format('MM/DD/YYYY'),
      },
    }
  }

  static quoteAuthorizationExtendedToSupplier(params: {
    customerName: string
    quoteNumber: string
    authorizationDeadline: Moment
  }): EmailNotification {
    return {
      templateId: TEMPLATES.QUOTE_AUTHORIZATION_EXTENDED_TO_SUPPLIER,
      dynamicTemplateData: {
        ...params,
        authorizationDeadline:
          params.authorizationDeadline.format('MM/DD/YYYY'),
      },
    }
  }

  static quoteAuthorizationNearingExpirationToSupplier(
    paymentType: InvoicePaymentType | null,
    params: {
      customerName: string
      supplierName: string
      quoteNumber: string
      loginUrl: string
    },
  ): EmailNotification {
    return {
      templateId:
        paymentType === InvoicePaymentType.FACTORING
          ? TEMPLATES.IHC_QUOTE_NEARING_EXPIRATION_TO_SUPPLIER
          : TEMPLATES.QUOTE_AUTHORIZATION_NEARING_EXPIRATION_TO_SUPPLIER,
      dynamicTemplateData: params,
    }
  }

  static quoteAuthorizationExpired(params: {
    customerName: string
    supplierName: string
    quoteNumber: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.QUOTE_AUTHORIZATION_EXPIRED,
      dynamicTemplateData: params,
    }
  }

  static quoteAuthorizationExpiredToSupplier(
    paymentType: InvoicePaymentType | null,
    params: {
      supplierName: string
      customerName: string
      quoteNumber: string
      loginUrl: string
    },
  ): EmailNotification {
    return {
      templateId:
        paymentType === InvoicePaymentType.FACTORING
          ? TEMPLATES.IHC_QUOTE_EXPIRED_TO_SUPPLIER
          : TEMPLATES.QUOTE_AUTHORIZATION_EXPIRED_TO_SUPPLIER,
      dynamicTemplateData: params,
    }
  }
  //endregion Quote

  //region Invitations
  static newCustomerInvitation(
    templateId: string | null,
    params: {
      link: string
      fullName: string
      companyName: string
      supplierName: string
      invitedBy: string
      email: string
      phone: string
      logoUrl: string | undefined
    },
  ): EmailNotification {
    return {
      templateId: templateId ?? TEMPLATES.NEW_CUSTOMER_INVITATION,
      dynamicTemplateData: params,
    }
  }

  static newUserInvitation(params: {
    customerName: string
    supplierName: string
    supplierEmail: string
    supplierPhone: string
    invitationLink: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.NEW_USER_INVITATION,
      dynamicTemplateData: params,
    }
  }
  //endregion

  //region Factoring
  static newCustomerInvitationFactoring(
    templateId: string | null,
    params: {
      customerName: string
      supplierName: string
      invitationLink: string
      logoUrl: string | undefined
    },
  ): EmailNotification {
    return {
      templateId: templateId ?? TEMPLATES.NEW_CUSTOMER_INVITATION_FACTORING,
      dynamicTemplateData: params,
    }
  }

  static invoiceApprovedForFactoring(params: {
    companyName: string
    invoiceNumber: string
    invoiceAmount: number
    loginUrl: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.INVOICE_APPROVED_FOR_FACTORING,
      dynamicTemplateData: {
        ...params,
        invoiceAmount: toCurrency(params.invoiceAmount),
      },
    }
  }

  static invoiceRejectedForFactoring(params: {
    companyName: string
    invoiceNumber: string
    invoiceAmount: number
    loginUrl: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.INVOICE_REJECTED_FOR_FACTORING,
      dynamicTemplateData: {
        ...params,
        invoiceAmount: toCurrency(params.invoiceAmount),
      },
    }
  }
  //endregion Factoring

  // region Payments
  static invoiceProcessingToSupplier(params: {
    customerName: string
    invoiceAmount: number
    totalProcessingAmount: number
    invoiceNumber: string
    paymentType: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.INVOICE_PROCESSING_TO_SUPPLIER,
      dynamicTemplateData: {
        ...params,
        totalProcessingAmount: toCurrency(params.totalProcessingAmount),
        amount: toCurrency(params.invoiceAmount),
      },
    }
  }

  static invoiceProcessingToCustomer(params: {
    supplierName: string
    invoiceNumber: string
    invoiceDueDate: Date | string
    paymentMethod: string
    invoiceAmount: number
    serviceFee: number
    paymentAmount: number
    totalPaid: number
    logoUrl: string | undefined
  }): EmailNotification {
    return {
      templateId: TEMPLATES.INVOICE_PROCESSING_TO_CUSTOMER,
      dynamicTemplateData: {
        ...params,
        invoiceAmount: toCurrency(params.invoiceAmount),
        invoiceDueDate: moment(params.invoiceDueDate).format('MM/DD/YYYY'),
        serviceFee: toCurrency(params.serviceFee),
        paymentAmount: toCurrency(params.paymentAmount),
        totalPaid: toCurrency(params.totalPaid),
      },
    }
  }

  static invoiceProcessingToGuestPayer(params: {
    supplierName: string
    invoiceNumber: string
    invoiceDueDate: Date | string
    paymentMethod: string
    invoiceAmount: number
    serviceFee: number
    totalPaid: number
    signupLink: string
    logoUrl: string | undefined
  }): EmailNotification {
    return {
      templateId: TEMPLATES.INVOICE_PROCESSING_TO_GUEST_PAYER,
      dynamicTemplateData: {
        ...params,
        invoiceAmount: toCurrency(params.invoiceAmount),
        invoiceDueDate: moment(params.invoiceDueDate).format('MM/DD/YYYY'),
        serviceFee: toCurrency(params.serviceFee),
        totalPaid: toCurrency(params.totalPaid),
      },
    }
  }

  static ihcAutopayPaymentProcessingToCustomer(params: {
    customerName: string
    invoiceNumber: string
    paymentDate: Date | string
    amountCharged: number
    processingFee: number
    totalCharged: number
    paymentMethod: string
  }): EmailNotification {
    return {
      templateId: TEMPLATES.IHC_AUTOPAY_PAYMENT_PROCESSING,
      dynamicTemplateData: {
        ...params,
        totalCharged: toCurrency(params.totalCharged),
        processingFee: toCurrency(params.processingFee),
        amountCharged: toCurrency(params.amountCharged),
        paymentDate: moment(params.paymentDate).format('MM/DD/YYYY'),
      },
    }
  }

  static ihcAutopayPaymentFailedToCustomer(params: {
    customerName: string
    invoiceNumber: string
    paymentDate: Date | string
    amount: number
    paymentMethod: string
    failureReason?: string
  }): EmailNotification {
    const templateData: any = {
      customerName: params.customerName,
      invoiceNumber: params.invoiceNumber,
      paymentDate: moment(params.paymentDate).format('MM/DD/YYYY'),
      amount: toCurrency(params.amount),
      paymentMethod: params.paymentMethod,
    }

    if (params.failureReason) {
      templateData.failureReason = params.failureReason
    }

    return {
      templateId: TEMPLATES.IHC_AUTOPAY_PAYMENT_FAILED_CUSTOMER,
      dynamicTemplateData: templateData,
    }
  }

  static ihcAutopayPaymentFailedToOps(params: {
    customerName: string
    customerEmail: string
    invoiceNumber: string
    invoiceAmount: number
    failureReason?: string
    failureDate: Date | string
  }): EmailNotification {
    const templateData: any = {
      customerName: params.customerName,
      customerEmail: params.customerEmail,
      invoiceNumber: params.invoiceNumber,
      invoiceAmount: toCurrency(params.invoiceAmount),
      failureDate: moment(params.failureDate).format('MM/DD/YYYY'),
    }

    if (params.failureReason) {
      templateData.failureReason = params.failureReason
    }

    return {
      templateId: TEMPLATES.IHC_AUTOPAY_PAYMENT_FAILED_OPS,
      dynamicTemplateData: templateData,
    }
  }

  static operationCheckError(params: {
    transactionId: string
    operationId: string
    error: string
  }): EmailNotification {
    return {
      subject: `An error occurred while checking the operation result for transaction ${params.transactionId}`,
      html: `<div>
                An error occurred while checking the operation result:
                <br /><br />
                Transaction ID: <b>${params.transactionId}</b><br />
                Operation ID: <b>${params.operationId}</b><br />
                <pre>${params.error}</pre>
              </div>`,
    }
  }
  // endregion Payments

  //region Approvals
  static getPaidAgreementApprovalRequest(params: {
    inviteeName: string
    applicantName: string
    companyName: string
    link: string
    attachments: AttachmentData[]
  }): EmailNotification {
    return {
      templateId: TEMPLATES.GET_PAID_AGREEMENT_APPROVAL_REQUEST,
      dynamicTemplateData: params,
      attachments: params.attachments,
    }
  }
  //endregion

  private static paragraph(text: string): string {
    return `<p>${text}</p>`
  }

  private static formatIndex(index: number): string {
    switch (index) {
      case 1:
        return '1st'
      case 2:
        return '2nd'
      case 3:
        return '3rd'
      default:
        return `${index}th`
    }
  }
}
