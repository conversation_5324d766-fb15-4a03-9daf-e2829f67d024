import {
  dictionaries,
  exceptions,
  IEncrypted,
  InvoicePaymentType,
} from '@linqpal/models'
import moment from 'moment'
import mongoose, { Types } from 'mongoose'
import {
  ACH_TRANSACTION_TYPE,
  Company,
  CustomerAccount,
  Invoice,
  Operation,
  Transaction,
  User,
  UserRole,
} from '../models'
import {
  IBankAccount,
  ICompany,
  ICustomerAccount,
  IInvoice,
  IOperation,
} from '../models/types'
import { emailService } from './email.service'
import smsService from './sms.service'
import { cardPayment } from './tabapay'
import EmailBuilder from '../helpers/EmailBuilder'
import { AwsService, LMS, Slack } from '../../index'
import { customErrorToString } from '../helpers/SnsEventBuilder'
import { FactoringService } from './factoring'
import { CriticalError } from '@linqpal/models/src/types/exceptions'
import { ArAdvanceStatus } from '@linqpal/models/src/dictionaries/factoring'
import { LoanApplicationService } from './loanApplication.service'
import { IPaymentInfo } from './payment/types'
import { PAYMENT_METHODS } from '@linqpal/models/src/dictionaries'
import { capitalize } from 'lodash'
import { sendIhcAutopayFailureNotifications } from './payment/invoicePostPaymentActions.service'

type PaymentMethods = typeof dictionaries.PAYMENT_METHODS

export async function checkPaymentMethod(
  invoices_id: string[],
  payment_method: PaymentMethods[keyof PaymentMethods],
  session: mongoose.ClientSession,
) {
  const transactions = await Operation.aggregate(
    [
      {
        $match: {
          owner_id: { $in: invoices_id },
          status: { $ne: dictionaries.OPERATION_STATUS.DECLINED },
          'metadata.payment_method': { $ne: payment_method },
          type: { $ne: dictionaries.OPERATION_TYPES.FACTORING.DISBURSEMENT },
        },
      },
      {
        $lookup: {
          from: Transaction.collection.name,
          as: 'transaction',
          let: { operation_id: { $toString: '$_id' } },
          pipeline: [
            { $match: { $expr: { $eq: ['$operation_id', '$$operation_id'] } } },
            {
              $match: {
                payment_method: dictionaries.PAYMENT_METHODS.ACH,
                status: dictionaries.TRANSACTION_STATUS.SUCCESS,
                'metadata.transactionType': ACH_TRANSACTION_TYPE.OUT,
              },
            },
          ],
        },
      },
      { $unwind: '$transaction' },
    ],
    { session },
  )
  if (transactions.length > 0) {
    throw new exceptions.LogicalError('invoice/invalid–payment-method')
  }
}

interface IMakeCardPaymentParams {
  invoices: IInvoice[]
  accountId: string
  payerCompanyId: string
  session: mongoose.ClientSession
  userId: string
  commitTransaction?: boolean
  sendNotifications?: boolean
  userRequestedAmount?: number
  isAutoDebit?: boolean
}

export const makeCardPayment = async ({
  invoices,
  accountId,
  payerCompanyId,
  session,
  userId,
  commitTransaction = true,
  sendNotifications = true,
  userRequestedAmount,
  isAutoDebit,
}: IMakeCardPaymentParams): Promise<IPaymentInfo> => {
  let eventSourceBasis
  const invoiceIds: string[] = invoices.map((inv) => inv._id.toString())
  try {
    const factoringInvoice = invoices.find(
      (inv) =>
        inv.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
        inv.paymentDetails.arAdvanceStatus === ArAdvanceStatus.Approved,
    )

    if (!factoringInvoice) {
      eventSourceBasis = `checkPaymentMethod`
      await checkPaymentMethod(
        invoiceIds,
        dictionaries.PAYMENT_METHODS.CARD,
        session,
      )
    }

    const operations: IOperation[] = []
    let supplierId: string | undefined
    for (const invoice of invoices) {
      const eventInvoiceBasis = `(called for invoice: ${invoice._id.toString()})`
      supplierId = invoice.company_id
      const supplierCompany = await Company.findById(supplierId).session(
        session,
      )
      eventSourceBasis = `checkSupplierCompany ${eventInvoiceBasis}`
      checkSupplierCompany(supplierCompany)

      eventSourceBasis = `getOperation ${eventInvoiceBasis}`
      const operation = await getOperation(invoice, supplierId, session)

      operation.metadata.payer_id = payerCompanyId
      operation.metadata.payment_method = dictionaries.PAYMENT_METHODS.CARD
      operation.metadata.paymentInitiator = 'customer'
      operation.markModified('metadata')
      operations.push(operation)
    }

    eventSourceBasis = `cardPayment`

    const paymentInfo = await cardPayment(
      operations,
      {
        transactionType: dictionaries.TABAPAY_TRANSACTION_TYPES.PULL,
        sourceAccountId: accountId,
        destinationAccountId:
          process.env.LP_TABAPAY_SETTLEMENT_ACCOUNT_ID || '',
        supplierId: supplierId!,
        reason: `Invoice payment, customerID: ${payerCompanyId} merchantID: ${supplierId}`,
        sendNotifications: sendNotifications,
        userRequestedAmount,
        isAutoDebit,
      },
      session,
    )

    if (factoringInvoice) {
      eventSourceBasis = `handleFactoringInvoice`
      const loan = (
        await LMS.findLoans({
          payableId: factoringInvoice.id,
          detailed: true,
        })
      )?.[0]

      const factoringOperation = operations.find(
        (op) => op.owner_id === factoringInvoice.id,
      )

      if (!factoringOperation) {
        throw new Error('Factoring operation not found')
      }

      const pullTransactions = await Transaction.find(
        {
          operation_id: factoringOperation.id,
          'metadata.transactionType': ACH_TRANSACTION_TYPE.PULL,
        },
        null,
        { session },
      ).sort({ updatedAt: -1 })

      const pullTransaction = pullTransactions[0]

      eventSourceBasis = `LMS:performPayment`
      const payment = await LMS.performPayment(
        loan.id,
        pullTransaction.amount,
        isAutoDebit,
      )

      eventSourceBasis = `updateTransaction`
      pullTransaction.metadata.lms_paymentId = payment.id

      if (isAutoDebit) {
        if (!pullTransaction.manualPaymentData) {
          pullTransaction.manualPaymentData = {}
        }
        pullTransaction.manualPaymentData.manual_payment_method = capitalize(
          PAYMENT_METHODS.CARD,
        )
        pullTransaction.markModified('manualPaymentData')
      }
      pullTransaction.markModified('metadata')
      await pullTransaction.save({ session })

      if (factoringOperation) {
        factoringOperation.paidAmount =
          (factoringOperation.paidAmount || 0) + pullTransactions[0].amount
        factoringOperation.markModified('metadata')
        await factoringOperation.save({ session })
      }
    } else {
      try {
        await FactoringService.cancelFactoringForInvoicesIfAny(
          invoices,
          session,
        )
      } catch (err) {
        throw new CriticalError("Can't cancel factoring for invoices", {
          invoices: invoices.map((el) => el.toJSON()),
          accountId,
          payerCompanyId,
          cause: err,
        })
      }
    }

    for (const invoiceId of invoiceIds) {
      eventSourceBasis = `sendSQSMessage NETSUITE (sent for invoice: ${invoiceId})`
      await AwsService.sendSQSMessage(
        'netsuite-connector',
        JSON.stringify({
          id: invoiceId,
          operationType: 'InvoiceStatusUpdate',
          status: 'PaymentProcessing',
        }),
        'NETSUITE',
      )
    }

    await Promise.all(
      invoices.map((invoice) =>
        LoanApplicationService.detachInvoice(invoice.id, null, session),
      ),
    )

    //Probably needed to be replaced
    if (commitTransaction) {
      // Review - if possible move commit to a caller side everywhere
      await session.commitTransaction()
    }

    return paymentInfo
  } catch (err) {
    console.log(err)

    await sendIhcAutopayFailureNotifications(
      invoiceIds,
      dictionaries.PAYMENT_METHODS.CARD,
      isAutoDebit || false,
      userRequestedAmount,
      err,
      accountId,
    )

    await Slack.notifyError(
      'makeCardPayment',
      `Function: ${eventSourceBasis} UserId: ${userId} CompanyId: ${payerCompanyId} InvoiceIds: ${invoiceIds.join(
        ',',
      )}`,
      customErrorToString(err),
    )

    throw err
  }
}

export const collectCardPayment = async (
  invoiceId: string,
  accountId: string,
  supplierCompany: ICompany,
  host: string,
  session: mongoose.ClientSession,
) => {
  if (!supplierCompany.settings.supplierCanPay) {
    throw new exceptions.LogicalError('Operation is not supported')
  }

  const invoice = await Invoice.findById(invoiceId).session(session)
  console.log(invoiceId, invoice?.company_id, supplierCompany.id)
  if (!invoice || invoice.company_id !== supplierCompany.id) {
    throw new exceptions.LogicalError('invoice/not-found')
  }

  checkSupplierCompany(supplierCompany)
  const customerAccount = await getCustomer(invoice, invoice.company_id)
  const customerUserCompanyId = await getCustomerUserCompany(
    customerAccount._id.toString(),
  )
  const bankAccount = getCustomerBankAccount(customerAccount, accountId)
  const operation = await getOperation(invoice, supplierCompany.id, session)

  operation.metadata.payment_method = dictionaries.PAYMENT_METHODS.CARD
  operation.metadata.paymentInitiator = 'company'
  if (customerUserCompanyId) {
    operation.metadata.payer_id = customerUserCompanyId
  }
  operation.markModified('metadata')

  await cardPayment(
    [operation],
    {
      transactionType: dictionaries.TABAPAY_TRANSACTION_TYPES.PULL,
      sourceAccountId: bankAccount.cardMetadata.accountId,
      destinationAccountId: process.env.LP_TABAPAY_SETTLEMENT_ACCOUNT_ID || '',
      supplierId: supplierCompany.id,
      reason: `Invoice payment collected, customerID: ${invoice.customer_account_id} merchantID: ${supplierCompany.id}`,
      sendNotifications: false,
    },
    session,
  )

  await updateCustomerAccountLastPurchasedDate(invoice.customer_account_id)
  await sendMessageToCustomer(
    supplierCompany,
    customerAccount,
    invoice,
    bankAccount,
    host,
  )
}

function checkSupplierCompany(supplierCompany: ICompany | null): void {
  if (!supplierCompany) {
    throw new exceptions.LogicalError('Company not found')
  }

  if (
    !supplierCompany.settings.cardPricingPackageId ||
    supplierCompany.settings.cardPricingPackageId === 'optOut'
  ) {
    throw new exceptions.LogicalError(
      `The supplier does not accept this payment type.`,
    )
  }
}

export async function checkStatusAndGetOperation(
  invoice: IInvoice,
  session: mongoose.ClientSession | null,
) {
  const operation = await Operation.findOne({
    owner_id: invoice.id,
    status: mongoose.trusted({
      $ne: dictionaries.OPERATION_STATUS.DECLINED,
    }),
  }).session(session)

  if (!operation) return null

  const totalPaidAmount =
    (operation.paidAmount || 0) + (operation.processingAmount || 0)

  const totalInvoiceAmount =
    invoice.total_amount + (invoice?.paymentDetails?.fees || 0)

  const isFactoringInvoice =
    invoice.paymentDetails?.paymentType === InvoicePaymentType.FACTORING &&
    invoice.paymentDetails.arAdvanceStatus === ArAdvanceStatus.Approved

  const hasRemainingAmount = totalInvoiceAmount > totalPaidAmount

  if (
    operation &&
    (operation.status === dictionaries.OPERATION_STATUS.SUCCESS ||
      (operation.status === dictionaries.OPERATION_STATUS.PROCESSING &&
        !(isFactoringInvoice && hasRemainingAmount)))
  ) {
    throw new exceptions.LogicalError('Invoice already paid')
  }

  return operation
}

export async function getOperation(
  invoice: IInvoice,
  payeeId: string,
  session: mongoose.ClientSession | null,
): Promise<IOperation> {
  let operation = await checkStatusAndGetOperation(invoice, session)

  if (!operation) {
    ;[operation] = await Operation.create(
      [
        {
          owner_id: invoice.id,
          amount: invoice.total_amount,
          date: moment().toDate(),
          type: dictionaries.OPERATION_TYPES.INVOICE.PAYMENT,
          status: dictionaries.OPERATION_STATUS.PLACED,
          metadata: { payee_id: payeeId },
        },
      ],
      { session: session },
    )
  }

  return operation
}

async function getCustomer(
  invoice: IInvoice,
  companyId: string,
): Promise<ICustomerAccount> {
  const customerAccount = await CustomerAccount.findOne({
    _id: invoice.customer_account_id,
    company_id: companyId,
  }).populate('bankAccounts')

  if (!customerAccount) {
    throw new exceptions.LogicalError('Customer account not found')
  }

  return customerAccount
}

async function getCustomerUserCompany(id: string): Promise<string | null> {
  const customerAccount = await CustomerAccount.aggregate([
    {
      $match: {
        _id: new Types.ObjectId(id),
      },
    },
    {
      $addFields: {
        email: { $ifNull: ['$email', ''] },
        phone: { $ifNull: ['$phone', ''] },
      },
    },
    {
      $lookup: {
        from: User.collection.name,
        let: { email: '$email', phone: '$phone' },
        pipeline: [
          {
            $match: {
              $expr: {
                $cond: {
                  if: { $ne: ['$$email', ''] },
                  then: { $eq: ['$email', '$$email'] },
                  else: {
                    $or: [
                      { $eq: ['$login', '$$email'] },
                      { $eq: ['$login', '$$phone'] },
                    ],
                  },
                },
              },
            },
          },
          {
            $project: {
              sub: 1,
            },
          },
        ],
        as: 'users',
      },
    },
    { $unwind: { path: '$users', preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: UserRole.collection.name,
        let: { sub: '$users.sub' },
        pipeline: [
          { $match: { $expr: { $eq: ['$sub', '$$sub'] } } },
          { $project: { company_id: 1 } },
        ],
        as: 'roles',
      },
    },
    { $unwind: { path: '$roles', preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        company_id: '$roles.company_id',
      },
    },
    {
      $project: {
        _id: 0,
        company_id: 1,
      },
    },
  ])

  if (!customerAccount || customerAccount.length === 0) {
    throw new exceptions.LogicalError('Customer account not found')
  }

  return customerAccount[0]?.company_id || null
}

function getCustomerBankAccount(
  customerAccount: ICustomerAccount,
  accountId: string,
): IBankAccount {
  const bankAccount = customerAccount.bankAccounts?.find(
    (x) =>
      x.id === accountId && !x.isDeactivated && x.paymentMethodType === 'card',
  )

  if (!bankAccount || bankAccount.paymentMethodType !== 'card') {
    throw new exceptions.LogicalError('Bank account not found')
  }

  return bankAccount
}

async function updateCustomerAccountLastPurchasedDate(
  customerAccountId: string,
) {
  await CustomerAccount.findOneAndUpdate(
    { _id: customerAccountId },
    { last_purchase_date: moment().toDate() },
  ).catch((error) => {
    console.log('Customer Account could not be updated.', error)
  })
}

async function sendMessageToCustomer(
  supplierCompany: ICompany,
  customerAccount: ICustomerAccount,
  invoice: IInvoice,
  bankAccount: IBankAccount,
  host: string,
): Promise<void> {
  if (!customerAccount.email && !customerAccount.phone) {
    return
  }

  if (customerAccount.phone) {
    const smsMessage = smsService.getMessage({
      key: 'paymentCollected',
      data: {
        supplierName: supplierCompany.name,
        invoiceAmount: invoice.total_amount,
        invoiceNumber: invoice.invoice_number,
        invoiceLink: `${host}/receipt?id=${invoice.id}`,
        accountNumber:
          (bankAccount.accountNumber as IEncrypted).display?.replace(
            /\*/g,
            '',
          ) || '',
      },
    })

    await smsService.send(customerAccount.phone, smsMessage)
  }

  if (customerAccount.email) {
    const emailMessageData = EmailBuilder.getSubjectAndBody({
      key: 'paymentCollected',
      data: {
        supplierName: supplierCompany.name,
        invoiceAmount: invoice.total_amount,
        invoiceNumber: invoice.invoice_number,
        invoiceLink: `${host}/receipt?id=${invoice.id}`,
        accountNumber:
          (bankAccount.accountNumber as IEncrypted).display?.replace(
            /\*/g,
            '',
          ) || '',
      },
    })

    await emailService.send({
      to: customerAccount.email,
      subject: emailMessageData.subject,
      text: emailMessageData.body,
    })
  }
}
