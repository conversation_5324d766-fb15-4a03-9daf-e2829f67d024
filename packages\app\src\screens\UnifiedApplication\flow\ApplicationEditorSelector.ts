import { FC } from 'react'
import { Steps } from '../../GeneralApplication/Store/ApplicationSteps'

import details from '../../GeneralApplication/Application/flow/bank/BankDetailsStep'
import businessAddress from '../../GeneralApplication/Application/flow/businessInfo/businessAddress'
import businessPhone from '../../GeneralApplication/Application/flow/businessInfo/businessPhone'
import ein from '../../GeneralApplication/Application/flow/businessInfo/ein'
import startDate from '../../GeneralApplication/Application/flow/businessInfo/startDate'
import trade from '../../GeneralApplication/Application/flow/businessInfo/trade'
import address from '../../GeneralApplication/Application/flow/businessOwner/address'
import ownershipPercentage from '../../GeneralApplication/Application/flow/businessOwner/ownershipPercentage'
import authorisedDetails from '../../GeneralApplication/Application/flow/businessOwner/authorisedDetails'
import birthdate from '../../GeneralApplication/Application/flow/businessOwner/birthdate'
import isAuthorised from '../../GeneralApplication/Application/flow/businessOwner/isAuthorised'
import isOwner from '../../GeneralApplication/Application/flow/businessOwner/isOwner'
import ssn from '../../GeneralApplication/Application/flow/businessOwner/ssn'
import debt from '../../GeneralApplication/Application/flow/finance/debt'
import howMuchCredit from '../../GeneralApplication/Application/flow/finance/howMuchCredit'
import revenue from '../../GeneralApplication/Application/flow/finance/revenue'
import coOwners from '../../GeneralApplication/Application/flow/coOwnerInfo/coOwners'
import arAdvanceRequestedLimit from '../../GeneralApplication/Application/flow/finance/arAdvanceRequestedLimit'
import { EmailStep } from './businessInfo/EmailStep'
import { CategoryStep } from './businessInfo/CategoryStep'
import { BusinessNameStep } from './businessInfo/BusinessNameStep'
import { UnifiedApplicationStore } from '../store/UnifiedApplicationStore'
import { ReviewStep } from './review/ReviewStep'
import type from '../../GeneralApplication/Application/flow/businessInfo/type'

// TODO: VK: Unified: Review
// TODO: VK: Unified: Remove extra type options from title, description (just for backward compatibility for refactoring time)

export interface IApplicationStepEditorTitle {
  text?: string | (() => string)
  style?: any
}

export interface IApplicationStepDescription {
  text?: string
  style?: any
}

export interface IApplicationStepOptions {
  title?: IApplicationStepEditorTitle
  description?: IApplicationStepDescription | string
  decidingQuestion?: boolean
  canGoBack?: boolean
  canMoveNext?: boolean
  canSkip?: boolean
  showGroupTitle?: boolean
  onMoveBack?: (store: UnifiedApplicationStore) => boolean
  beforeMoveNext?: () => void
}

export interface IApplicationStepEditor {
  options?: IApplicationStepOptions
  component: FC<any> | ((props: any) => JSX.Element)
}

export function getApplicationStepEditor(path: string): IApplicationStepEditor {
  switch (path) {
    // Business Info
    case Steps.businessInfo.email.path:
      return EmailStep
    case Steps.businessInfo.category.path:
      return CategoryStep
    case Steps.businessInfo.businessName.path:
      return BusinessNameStep
    case Steps.businessInfo.trade.path:
      return trade as any
    case Steps.businessInfo.businessPhone.path:
      return businessPhone as any
    case Steps.businessInfo.businessAddress.path:
      return businessAddress as any
    case Steps.businessInfo.startDate.path:
      return startDate as any
    case Steps.businessInfo.type.path:
      return type as any
    case Steps.businessInfo.ein.path:
      return ein as any

    // Finance
    case Steps.finance.revenue.path:
      return revenue as any
    case Steps.finance.debt.path:
      return debt as any
    case Steps.finance.howMuchCredit.path:
      return howMuchCredit as any
    case Steps.finance.arAdvanceRequestedLimit.path:
      return arAdvanceRequestedLimit as any

    // Business Owner
    case Steps.businessOwner.isOwner.path:
      return isOwner as any
    case Steps.businessOwner.ownershipPercentage.path:
      return ownershipPercentage as any
    case Steps.businessOwner.isAuthorized.path:
      return isAuthorised as any
    case Steps.businessOwner.authorizedDetails.path:
      return authorisedDetails as any
    case Steps.businessOwner.address.path:
      return address as any
    case Steps.businessOwner.birthdate.path:
      return birthdate as any
    case Steps.businessOwner.ssn.path:
      return ssn as any

    // Co Owner Info
    case Steps.coOwnerInfo.coOwners.path:
      return coOwners as any

    // Bank
    case Steps.bank.details.path:
      return details as any

    // Review
    case Steps.review.review.path:
      return ReviewStep
    // case Steps.review.preview.path:
    //   return preview
    // case Steps.review.agreement.path:
    //   return agreement

    default:
      throw new Error(`no editor for path: ${path}`)
  }
}
