import React, { FC } from 'react'
import { observer } from 'mobx-react'
import { useTranslation } from 'react-i18next'
import {
  BtButton,
  BtDateInput,
  BtEncryptedAndSecuredBadge,
  BtInput,
  BtRadioGroup,
  BtSocialSecurityNumberInput,
  BtTaxIdInput,
} from '@linqpal/components/src/ui'
import { BtPercentageInput } from '@linqpal/components/src/ui/BtPercentageInput'
import { CoOwner } from './CoOwner'
import { View } from 'react-native'
import { Spacer } from '../../../../../../ui/atoms'
import { useResponsive } from '@linqpal/components/src/hooks'
import { BtPhoneInput } from '@linqpal/components/src/ui/BtPhoneInput'
import { AddressEditor } from '../../../../GeneralApplication/Application/flow/commonQuestions/AddressEditor'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import {
  validateDate,
  validateEmail,
} from '@linqpal/models/src/helpers/validations'
import ApplicationStore, {
  MIN_COOWNER_PERCENTAGE,
} from '../../../../GeneralApplication/Application/ApplicationStore'
import { CoOwnerAcceptanceWarning } from './CoOwnerAcceptanceWarning'
import { useCoOwnerStore } from './CoOwnersStore'

interface CoOwnerEditorProps {
  coOwner: CoOwner
  onSave: (coOwner: CoOwner) => void
  onCancel: () => void
}

export const CoOwnerEditor: FC<CoOwnerEditorProps> = observer(
  ({ coOwner, onSave, onCancel }) => {
    const { t } = useTranslation('application')
    const { sm } = useResponsive()
    const store = useCoOwnerStore()

    const validatePercentage = (value: string | number | undefined) => {
      const percentage = Number(value)
      const availablePercent = ApplicationStore.availableCoOwnerPercentage(
        ApplicationStore.currentCoOwnerIndex,
      )

      if (percentage < MIN_COOWNER_PERCENTAGE) {
        return t('ValidationErrors.InvalidMinimumPercentage')
      } else if (percentage > availablePercent) {
        return t('ValidationErrors.InvalidMaximumPercentage', {
          maxPercent: availablePercent,
        })
      } else {
        return ''
      }
    }

    const validateCoOwnerEmail = (email?: string) => {
      if (
        store.coOwners.some((c) => coOwner.id !== c.id && c.email === email)
      ) {
        return t('ValidationErrors.DuplicatedCoOwnerEmail')
      } else if (!validateEmail(email)) {
        return t('ValidationErrors.InvalidEmail')
      } else return ''
    }

    const validateBirthday = (value) =>
      value.length !== 10 || validateDate(value, 1900)
        ? ''
        : t('ValidationErrors.InvalidDate')

    const canSave = () =>
      !validatePercentage(coOwner.percentOwned) &&
      (!coOwner.email || !validateCoOwnerEmail(coOwner.email))

    return (
      <>
        <Spacer height={16} />
        <BtEncryptedAndSecuredBadge />
        <Spacer height={25} />
        <BtRadioGroup
          value={coOwner?.type}
          onChange={(e) => coOwner.setType(e)}
          options={[
            {
              label: t('CoOwners.IndividualOwner'),
              value: OwnerTypes.INDIVIDUAL,
            },
            {
              label: t('CoOwners.EntityOwner'),
              value: OwnerTypes.ENTITY,
            },
          ]}
          radioStyle={{ marginRight: 32 }}
          groupStyle={{ flexDirection: 'row' }}
          testID="coOwner_type"
        />
        <Spacer height={15} />
        <BtPercentageInput
          value={coOwner?.percentOwned || 0}
          onChangeText={(e) => coOwner?.setPercentage(e)}
          validate={validatePercentage}
          label={t('CoOwners.OwnershipPercentage')}
          testID="coOwner_percentage"
        />
        {coOwner?.type === OwnerTypes.INDIVIDUAL ? (
          <View style={{ flexDirection: sm ? 'row' : 'column', marginTop: 22 }}>
            <BtInput
              value={coOwner?.firstName || ''}
              onChangeText={(e) => coOwner?.setFirstName(e)}
              label={t('CoOwners.FirstName')}
              style={{ flexGrow: 1 }}
              testID="coOwner_firstName"
            />
            {sm ? <Spacer width={15} /> : <Spacer height={15} />}
            <BtInput
              value={coOwner?.lastName || ''}
              onChangeText={(e) => coOwner?.setLastName(e)}
              label={t('CoOwners.LastName')}
              style={{ flexGrow: 1 }}
              testID="coOwner_lastName"
            />
          </View>
        ) : (
          <>
            <BtInput
              value={coOwner?.entityName || ''}
              onChangeText={(e) => coOwner?.setEntityName(e)}
              style={{ marginTop: 22 }}
              label={t('CoOwners.EntityName')}
              testID="coOwner_entityName"
            />
            <View
              style={{ flexDirection: sm ? 'row' : 'column', marginTop: 22 }}
            >
              <BtInput
                value={coOwner?.firstName || ''}
                onChangeText={(e) => coOwner?.setFirstName(e)}
                label={t('CoOwners.AuthorizedRepresentativeFirstName')}
                style={{ flexGrow: 1 }}
                testID="coOwner_entityAuthfirstName"
              />
              {sm ? <Spacer width={15} /> : <Spacer height={15} />}
              <BtInput
                value={coOwner?.lastName || ''}
                onChangeText={(e) => coOwner?.setLastName(e)}
                label={t('CoOwners.AuthorizedRepresentativeLastName')}
                style={{ flexGrow: 1 }}
                testID="coOwner_entityAuthlastName"
              />
            </View>
          </>
        )}
        <Spacer height={15} />
        <AddressEditor
          model={coOwner}
          label={
            coOwner?.type === OwnerTypes.INDIVIDUAL
              ? t('CoOwners.HomeAddress')
              : t('CoOwners.EntityAddress')
          }
          testID="coOwner_address"
          collapsible={true}
        />
        <Spacer height={25} />
        {coOwner?.type === OwnerTypes.INDIVIDUAL ? (
          <>
            <BtDateInput
              value={coOwner?.birthday}
              onChangeText={(e) => coOwner?.setBirthday(e)}
              //eslint-disable-next-line i18next/no-literal-string
              format="MM/DD/YYYY"
              validate={validateBirthday}
              testID={'coOwner_birthday'}
              label={t('CoOwners.Birthday')}
            />
            <Spacer height={15} />
          </>
        ) : null}
        {coOwner?.type === OwnerTypes.INDIVIDUAL ? (
          <BtSocialSecurityNumberInput
            value={coOwner?.ssn}
            onChangeText={(e) => coOwner?.setSSN(e)}
            label={t('CoOwners.SSN')}
            validationError={t('ValidationErrors.InvalidSSN')}
            testID={'coOwner_ssn'}
          />
        ) : (
          <BtTaxIdInput
            value={coOwner.ein}
            onChangeText={(e) => coOwner.setEIN(e)}
            label={t('CoOwners.EIN')}
            validationError={t('ValidationErrors.InvalidTaxID')}
            testID={'coOwner_ein'}
          />
        )}
        <Spacer height={25} />
        <BtPhoneInput
          value={coOwner?.phone}
          onChangeText={(e) => coOwner?.setPhone(e.parsed)}
          label={t('CoOwners.PhoneNumber')}
          validationError={t('ValidationErrors.InvalidPhone')}
          testID={'coOwner_phone'}
        />
        <Spacer height={25} />
        <BtInput
          value={coOwner?.email}
          onChangeText={(e: string) => coOwner?.setEmail(e)}
          validateInput={(e?: string) => validateCoOwnerEmail(e)}
          label={t('CoOwners.EmailAddress')}
          testID="coOwner_email"
        />

        {ApplicationStore.isCreditApplication ? (
          <CoOwnerAcceptanceWarning style={{ marginTop: 30 }} />
        ) : null}

        <View
          style={{
            flexDirection: 'row',
            justifyContent: 'flex-end',
            marginTop: 30,
          }}
        >
          <BtButton
            onPress={onCancel}
            appearance={'ghost'}
            status={'basic'}
            style={{ marginRight: 20 }}
            testID="coOwner_cancel"
          >
            {t('CoOwners.Cancel')}
          </BtButton>
          <BtButton
            onPress={() => onSave(coOwner)}
            disabled={!canSave()}
            style={{ width: 150 }}
            testID="coOwner_save"
          >
            {t('CoOwners.Save')}
          </BtButton>
        </View>
      </>
    )
  },
)
