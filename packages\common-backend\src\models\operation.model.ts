import {
  dictionaries,
  Operation as OperationModel,
  Transaction as TransactionModel,
} from '@linqpal/models'
import createSchema from '../helpers/createSchema'
import { model, Schema } from 'mongoose'
import { IOperation, ITransaction } from './types'

const OperationSchema = createSchema(OperationModel)
OperationSchema.owner_id.index = true
OperationSchema.date.index = true
OperationSchema.date.type = Date
OperationSchema.firstTransactionDate.index = true
OperationSchema.firstTransactionDate.type = Date
OperationSchema.type.required = true
OperationSchema.type.enum = (
  Object.keys(dictionaries.OPERATION_TYPES) as Array<
    keyof typeof dictionaries.OPERATION_TYPES
  >
).reduce(
  (r: Array<string>, k: keyof typeof dictionaries.OPERATION_TYPES) => [
    ...r,
    ...Object.values(dictionaries.OPERATION_TYPES[k]),
  ],
  [],
)
OperationSchema.status.enum = Object.values(dictionaries.OPERATION_STATUS)
OperationSchema.status.required = true
OperationSchema.metadata.type = Schema.Types.Mixed
OperationSchema.amount.required = true
OperationSchema.payment_provider = {
  type: String,
  default: null,
  index: true,
}

const operationSchema = new Schema<IOperation>(OperationSchema, {
  timestamps: true,
})

operationSchema.virtual('transactions', {
  ref: 'Transaction',
  localField: '_id',
  foreignField: 'operation_id',
})

operationSchema.pre('save', function () {
  if (!this.date) {
    this.set({ date: new Date() })
  }
})
const Operation = model<IOperation>('Operation', operationSchema)

const TransactionSchema = createSchema(TransactionModel)
TransactionSchema.operation_id.index = true
TransactionSchema.operation_id.required = true
TransactionSchema.payer_id.index = true
TransactionSchema.payee_id.index = true
TransactionSchema.date.index = true
TransactionSchema.date.type = Date
TransactionSchema.type.enum = (
  Object.keys(dictionaries.TRANSACTION_TYPES) as Array<
    keyof typeof dictionaries.TRANSACTION_TYPES
  >
).reduce(
  (r: Array<string>, k) => [
    ...r,
    ...Object.values(dictionaries.TRANSACTION_TYPES[k]),
  ],
  [],
)
TransactionSchema.type.required = true
TransactionSchema.amount.required = true
TransactionSchema.payment_method.enum = Object.values(
  dictionaries.PAYMENT_METHODS,
)
TransactionSchema.payment_method.required = true
TransactionSchema.status.enum = Object.values(dictionaries.TRANSACTION_STATUS)
TransactionSchema.status.required = true
TransactionSchema.metadata.type = Schema.Types.Mixed
TransactionSchema.manualPaymentData = {
  type: Schema.Types.Mixed,
  required: false,
}

const transactionSchema = new Schema<ITransaction>(TransactionSchema)
const Transaction = model<ITransaction>('Transaction', transactionSchema)

export { Operation, Transaction }
