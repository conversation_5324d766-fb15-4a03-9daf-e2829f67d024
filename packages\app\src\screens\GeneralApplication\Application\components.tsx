import React, { useMemo, useState } from 'react'
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native'
import {
  BtAlert,
  BtAlertProps,
  BtButton,
  BtLink,
  BtText,
  BtTitle,
} from '@linqpal/components/src/ui'
import { composeStyle } from '@linqpal/components/src/helpers'
import {
  AtIcon,
  BlackArrowBack,
  FolderIcon,
  IconArrowBack,
  IconCloseSquare,
  IconNotepad,
  PersonWithCheck,
  SuccessGreenCheck,
  WarningWithCircle,
} from '../../../assets/icons'
import { useResponsive } from '../../../utils/hooks'
import { useNavigation } from '@react-navigation/core'
import { Trans, useTranslation } from 'react-i18next'
import { observer } from 'mobx-react'
import { routes } from '@linqpal/models'
import {
  MESSAGE_TYPE,
  useMessage,
} from '../../../utils/helpers/MessageProvider'
import ApplicationStore from './ApplicationStore'
import { colors } from '@linqpal/components/src/theme'
import { Spacer } from '../../../ui/atoms'
import { paths } from '../../links'
import RootStore from '../../../store/RootStore'
import { useStore } from '../../../store'
import { ActivityIndicator } from 'react-native-paper'
import { ConsentStatement } from './components/ConsentStatement'
import { CompanyUtils } from '@linqpal/models/src/helpers/companyUtils'

// done
export const Header = ({ sm, t, onClose, skip, onSkip, title }) => {
  return (
    <View
      style={{
        width: '100%',
        height: sm ? 72 : 54,
        justifyContent: 'space-between',
        alignItems: 'center',
        flexDirection: 'row',
        borderBottomColor: '#EBEEEF',
        borderBottomWidth: 1,
        paddingHorizontal: sm ? 20 : 10,
      }}
    >
      <TouchableOpacity
        onPress={onClose}
        disabled={ApplicationStore.buttonLoading}
        style={composeStyle(
          {
            borderColor: '#E6EBEE',
            borderRadius: 6,
            borderWidth: 1,
            alignItems: 'center',
            justifyContent: 'center',
            width: 40,
            height: 40,
          },
          !sm && {
            borderWidth: 0,
            borderRadius: 8,
            backgroundColor: '#F5F7F8',
          },
        )}
      >
        <IconCloseSquare width={12} height={12} />
      </TouchableOpacity>

      <BtTitle
        style={{
          flex: 1,
          textAlign: 'center',
          fontSize: sm ? 20 : 16,
          fontWeight: sm ? '600' : '500',
        }}
      >
        {title || t('SetUpAccount')}
      </BtTitle>
      {!sm && skip ? (
        <BtText
          style={{ color: '#335C75', fontWeight: '500', marginRight: 10 }}
          onPress={onSkip}
        >
          {t('Skip')}
        </BtText>
      ) : (
        <Spacer width={50} />
      )}
    </View>
  )
}

// done
export const Title = ({ text }) => {
  const { sm } = useResponsive()

  return text && sm ? (
    <View style={{ padding: 20 }}>
      <BtTitle style={{ color: colors.accentText }} testID="ApplicationTitle">
        {text}
      </BtTitle>
    </View>
  ) : null
}

// done
export const DesktopButtons = observer(
  ({
    next,
    skip,
    sm,
    document,
    t,
    currentItem,
    onNext,
    disableNext,
    isBusy,
  }) => {
    if (!sm) return null

    const inviteLabel =
      currentItem === 'authorisedDetails'
        ? ApplicationStore.inviteButton(sm)
        : undefined

    const cameFromReview = ApplicationStore.previousStep?.includes('review')

    return (
      <View
        style={{
          flexDirection: 'row',
          width: '100%',
          justifyContent: 'flex-end',
          marginTop: 20,
        }}
      >
        {skip ? (
          <BtButton
            onPress={() => {
              if (cameFromReview) {
                document.setCurrent(ApplicationStore.previousStep)
                ApplicationStore.setPreviousStep('')
              } else if (typeof skip === 'function') {
                const ret = skip()
                if (ret) {
                  document.setCurrent(ret)
                }
              } else {
                document.setCurrent(skip)
              }
            }}
            appearance={'ghost'}
            style={{ marginRight: 20 }}
            testID="ApplicationSkipButton"
            disabled={isBusy}
          >
            {() => (
              <BtText
                style={{
                  fontWeight: '700',
                  fontSize: 16,
                  color: '#668598',
                }}
              >
                {cameFromReview
                  ? t('GoToReviewPage')
                  : inviteLabel
                  ? t('Next')
                  : t('SkipForNow')}
              </BtText>
            )}
          </BtButton>
        ) : null}
        {next ? (
          <BtButton
            disabled={isBusy || disableNext}
            onPress={onNext}
            style={{ width: inviteLabel ? 238 : 150 }}
            testID="ApplicationNextButton"
          >
            {inviteLabel || t('Next')}
          </BtButton>
        ) : null}
      </View>
    )
  },
)

// done
export const BackButton = observer(({ t, sm, onPress }) => {
  const color = sm ? colors.accentText : colors.primary

  return (
    <TouchableOpacity
      style={{
        flexDirection: 'row',
        alignItems: 'center',
      }}
      onPress={onPress}
      disabled={ApplicationStore.buttonLoading}
      testID="ApplicationBackButton"
    >
      {sm ? (
        <IconArrowBack stroke={color} />
      ) : (
        <BlackArrowBack stroke={color} style={{ height: 20 }} />
      )}
      <BtText
        style={{
          fontWeight: sm ? '500' : '700',
          color: color,
          marginTop: -2,
          marginLeft: sm ? 15 : 5,
        }}
      >
        {t('Back')}
      </BtText>
    </TouchableOpacity>
  )
})

export const SubmitButton = observer(({ t, canSubmit }) => {
  const navigation = useNavigation()
  const { agreeAndSubmit, buttonLoading, agreementsAccepted } = ApplicationStore

  const onAgree = () => {
    agreeAndSubmit(navigation)
  }

  return (
    <BtButton
      disabled={
        !canSubmit || buttonLoading || !agreementsAccepted || RootStore.isBusy
      }
      style={{
        width: 320,
        marginBottom: 36,
      }}
      onPress={onAgree}
      testID="ApplicationSubmitButton"
      loading={ApplicationStore.buttonLoading}
    >
      {t('Review.AgreeSubmit')}
    </BtButton>
  )
})

export const MobileButtons = observer(
  ({
    sm,
    t,
    document,
    next,
    onNext,
    onBack,
    currentItem,
    canSubmit,
    showFooterMessage,
    disableNext,
    isBusy,
    isReview,
  }) => {
    const navigation = useNavigation()
    const canGoBack = document?.filled.length && document?.filled.length > 0
    const {
      inviteButton,
      agreeAndSubmit,
      buttonLoading,
      agreementsAccepted,
      isGetPaidApplication,
      isCreditApplication,
    } = ApplicationStore

    const inviteLabel =
      currentItem === 'authorisedDetails' ? inviteButton(sm) : undefined

    return (
      <div
        style={{
          position: 'sticky',
          bottom: 0,
          alignSelf: 'baseline',
          width: '100%',
          backgroundColor: 'white',
        }}
      >
        {!(sm && isReview) && showFooterMessage && (
          <ConsentStatement canSubmit={canSubmit} document={document} />
        )}
        {!sm ? (
          <View
            style={{
              flexDirection: 'row',
              justifyContent: 'space-between',
              width: '100%',
              borderTopWidth: 1,
              borderTopColor: '#CCD6DD',
              padding: 20,
            }}
          >
            {canGoBack && currentItem !== 'review' ? (
              <BackButton t={t} sm={sm} onPress={onBack} />
            ) : null}
            {next && currentItem !== 'review' ? (
              <BtButton
                disabled={isBusy || disableNext}
                onPress={onNext}
                style={{
                  width: canGoBack
                    ? inviteLabel
                      ? 174
                      : sm
                      ? 160
                      : 85
                    : '100%',
                }}
                testID="ApplicationNextButton"
              >
                {inviteLabel || t('Next')}
              </BtButton>
            ) : null}
            {currentItem === 'review' && (
              <BtButton
                onPress={() => document.setSection('review.preview')}
                style={{ width: 160 }}
                testID="ApplicationReviewButton"
                disabled={isBusy}
                status="basic"
              >
                {() => (
                  <BtText
                    style={{
                      color: '#335C75',
                      fontWeight: '700',
                      fontSize: 16,
                    }}
                  >
                    {t('Review.ReviewButton')}
                  </BtText>
                )}
              </BtButton>
            )}
            {ApplicationStore.hasSubmissionRights &&
              ['review', 'preview'].includes(currentItem) &&
              (isGetPaidApplication || isCreditApplication ? (
                <BtButton
                  disabled={
                    !canSubmit ||
                    buttonLoading ||
                    !agreementsAccepted ||
                    RootStore.isBusy
                  }
                  style={{
                    width: 160,
                  }}
                  onPress={() => document.setSection('review.agreement')}
                  testID="ApplicationContinueToAgreementButton"
                  loading={buttonLoading}
                >
                  {t('Review.Continue')}
                </BtButton>
              ) : (
                <BtButton
                  disabled={!canSubmit || buttonLoading || RootStore.isBusy}
                  onPress={() => {
                    agreeAndSubmit(navigation)
                  }}
                  testID="ApplicationSubmitButton"
                >
                  {t('Review.AgreeSubmit')}
                </BtButton>
              ))}
          </View>
        ) : null}
      </div>
    )
  },
)

export const Question = ({ title, sm, t }) => {
  if (!title) return null

  const titleText = title instanceof Function ? title() : title

  return (
    <BtText
      style={composeStyle(
        {
          fontFamily: 'Inter, sans-serif',
          fontSize: 24,
          fontWeight: '600',
          color: '#003353',
          lineHeight: sm ? 36 : 26,
          marginBottom: 10,
        },
        !sm && { fontSize: 18, color: '#003353' },
      )}
    >
      {t(titleText)}
    </BtText>
  )
}

export const Description = ({ description, sm, t, style = null }) => {
  if (!description) return null

  if (typeof description !== 'string') {
    return description
  }

  return (
    <BtText
      style={composeStyle(
        {
          fontFamily: 'Inter, sans-serif',
          fontSize: 16,
          fontWeight: '600',
          color: '#335C75',
          marginBottom: 24,
          lineHeight: sm ? 30 : 22,
        },
        !sm && { fontSize: 16, marginBottom: 12 },
        style,
      )}
    >
      {t(description)}
    </BtText>
  )
}

export const QuestionLabel = ({ children }) => {
  return (
    <BtText
      style={{
        fontWeight: '700',
        fontSize: 12,
        lineHeight: 16,
        color: '#668598',
        marginTop: 16,
      }}
    >
      {children}
    </BtText>
  )
}

type IProgress = {
  progress: number
}

export const ProgressBar = ({ progress }: IProgress) => {
  const leftProgress = 1 - progress
  return (
    <View
      style={{
        width: '100%',
        flexDirection: 'row',
        height: 3,
      }}
    >
      <View
        style={{
          flex: progress,
          height: '100%',
          backgroundColor: colors.accentText,
        }}
      />
      <View
        style={{
          flex: leftProgress,
          height: '100%',
          backgroundColor: '#CCD6DD',
        }}
      />
    </View>
  )
}

export const Alert = (props: BtAlertProps) => {
  const { sm } = useResponsive()
  const buttonStyle = { minWidth: sm ? 230 : 142 }
  return (
    <BtAlert
      {...props}
      width={sm ? 650 : 356}
      buttons={[
        {
          status: 'basic',
          style: buttonStyle,
          ...props.buttons[0],
        },
        {
          style: buttonStyle,
          ...props.buttons[1],
        },
      ]}
      titleStyle={StyleSheet.flatten([
        sm ? styles.desktopAlertTitle : styles.mobileAlertTitle,
        props.titleStyle,
      ])}
    >
      {props.children}
    </BtAlert>
  )
}

export const CloseApplicationAlert = ({ onCloseAlert, onCloseApplication }) => {
  const { sm } = useResponsive()
  const navigation = useNavigation() as any
  const { t } = useTranslation('application')

  const saveForLater = () => {
    if (onCloseApplication) onCloseApplication()

    navigation.replace(paths.Console._self)
  }

  return (
    <Alert
      visible={true}
      title={t('CloseAlert.Title')}
      onClose={onCloseAlert}
      buttons={[
        {
          label: t('CloseAlert.SaveForLater'),
          onPress: saveForLater,
        },
        {
          label: sm
            ? t('CloseAlert.ContinueApplication')
            : t('CloseAlert.Continue'),
          onPress: () => onCloseAlert(),
        },
      ]}
      icon={<IconNotepad style={{ marginBottom: sm ? 33 : 18 }} />}
      buttonsContainerStyle={sm ? styles.buttonsContainer : undefined}
    >
      {sm && <Spacer height={16} />}
      <BtText
        style={composeStyle(
          sm ? styles.desktopAlertText : styles.mobileAlertText,
          styles.alertTextCommonStyle,
        )}
      >
        <Trans
          t={t}
          i18nKey={
            ApplicationStore.isCreditApplication
              ? 'CloseAlert.CreditRequestMessage'
              : ApplicationStore.isInHouseCreditApplication
              ? 'CloseAlert.InHouseCreditMessage'
              : 'CloseAlert.GetPaidApplicationMessage'
          }
          values={{
            supplierName: CompanyUtils.getCompanyName(
              RootStore.userStore.suppliers?.find(
                (s) => s.id === ApplicationStore.supplierId,
              ),
            ),
          }}
        />
      </BtText>
      {sm && <Spacer height={25} />}
    </Alert>
  )
}

const styles = StyleSheet.create({
  mobileAlertText: {
    fontSize: 16,
    width: 292,
  },
  desktopAlertText: {
    fontWeight: '500',
    fontSize: 18,
    width: 397,
  },
  alertTextCommonStyle: {
    lineHeight: 26,
    color: '#668598',
    textAlign: 'center',
  },
  mobileAlertTitle: {
    fontWeight: '600',
    fontSize: 18,
    lineHeight: 28,
    color: '#003353',
    width: 210,
  },
  desktopAlertTitle: {
    fontWeight: '700',
    fontSize: 28,
    lineHeight: 36,
    color: '#003353',
    width: 340,
  },
  buttonsContainer: { width: '80%', alignSelf: 'center' },
})

export const VerifyEmailAlert = ({ close, setClose }) => {
  const { sm } = useResponsive()
  const { setMessage } = useMessage()
  const { t } = useTranslation('application')
  const { userStore } = RootStore

  const sendAgain = () => {
    routes.user
      .sendVerificationEmail()
      .then(
        () =>
          setMessage(
            MESSAGE_TYPE.SUCCESS,
            'The link for verification is sent, please check your email',
          ),
        setClose(!close),
      )
  }

  return (
    <Alert
      visible={close}
      onClose={() => setClose(!close)}
      title={t('VerifyEmailAlert.Title')}
      type={'warning'}
      buttons={[
        {
          label: t('VerifyEmailAlert.Ok'),
          onPress: () => setClose(!close),
        },
        {
          label: t('VerifyEmailAlert.SendAgain'),
          onPress: sendAgain,
        },
      ]}
      icon={<AtIcon style={{ marginBottom: 32 }} />}
      height={sm ? 477 : 402}
      buttonsContainerStyle={sm ? styles.buttonsContainer : undefined}
    >
      <Spacer height={sm ? 10 : 4} />
      <BtText
        style={composeStyle(
          sm ? verifyEmailStyles.desktopAlertText : styles.mobileAlertText,
          verifyEmailStyles.alertTextCommonStyle,
        )}
      >
        <Trans
          t={t}
          i18nKey="VerifyEmailAlert.Message"
          values={{ email: userStore.user?.email }}
          components={{
            styled: (
              <Text
                style={composeStyle(
                  sm
                    ? verifyEmailStyles.desktopAlertText
                    : styles.mobileAlertText,
                  { fontWeight: sm ? '700' : '600' },
                )}
              />
            ),
          }}
        />
      </BtText>
      <Spacer height={20} />
    </Alert>
  )
}

const verifyEmailStyles = StyleSheet.create({
  desktopAlertText: {
    fontWeight: '400',
    fontSize: 18,
    width: 465,
  },
  mobileAlertTitle: {
    fontWeight: '600',
    fontSize: 18,
    lineHeight: 28,
    color: '#003353',
    width: 280,
  },
  alertTextCommonStyle: {
    lineHeight: 24,
    color: '#003353',
    textAlign: 'center',
  },
  desktopAlertTitle: {
    fontWeight: '600',
    fontSize: 28,
    lineHeight: 36,
    width: 400,
  },
})

export const InviteFormInfo = () => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()
  return (
    <View style={inviteInfoStyles.rootView}>
      <PersonWithCheck style={{ height: sm ? 27 : 40, width: sm ? 27 : 40 }} />
      <BtText
        style={composeStyle(
          inviteInfoStyles.text,
          !sm && { fontSize: 12, lineHeight: 18 },
        )}
      >
        {t('Finance.AuthorisedInfo')}
      </BtText>
    </View>
  )
}

const inviteInfoStyles = StyleSheet.create({
  rootView: {
    width: '100%',
    height: 76,
    flexDirection: 'row',
    paddingVertical: 11,
    paddingHorizontal: 26,
    marginTop: 10,
    backgroundColor: '#F5F7F8',
    borderRadius: 12,
    alignItems: 'center',
  },
  text: {
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 22,
    color: '#003353',
    marginLeft: 18,
  },
})

export const InvitationSent = ({ document }) => {
  const { t } = useTranslation('application')
  const { email, firstName, lastName } =
    ApplicationStore.authorisedInvitee ?? {}
  const { sm } = useResponsive()

  const getStyleForMobile = (style) => {
    return composeStyle(style, !sm && invitationSentStyles.mobileFont)
  }

  return email ? (
    <View style={invitationSentStyles.view1}>
      <PersonWithCheck
        style={{ marginLeft: 3, marginRight: 20, marginTop: 3 }}
      />
      <View style={invitationSentStyles.view3}>
        <Trans
          t={t}
          i18nKey="Review.InviteSent"
          values={{ name: `${firstName} ${lastName}` }}
          components={{
            styled: (
              <BtText
                style={getStyleForMobile(invitationSentStyles.boldText)}
              />
            ),
            simple: (
              <BtText style={getStyleForMobile(invitationSentStyles.text)} />
            ),
          }}
        />
        <View style={invitationSentStyles.view4}>
          <BtText style={getStyleForMobile(invitationSentStyles.boldText)}>
            {email}
          </BtText>
          <BtLink
            title={t('Review.Edit')}
            textStyle={getStyleForMobile(invitationSentStyles.linkText)}
            onPress={() => {
              ApplicationStore.setPreviousStep(document.current)
              document.setSection('businessOwner.authorisedDetails')
            }}
          />
        </View>
      </View>
    </View>
  ) : (
    <></>
  )
}

const invitationSentStyles = StyleSheet.create({
  view1: {
    width: '100%',
    borderRadius: 12,
    backgroundColor: '#F5F7F8',
    padding: 16,
    marginBottom: 45,
    flexShrink: 1,
    flexDirection: 'row',
  },
  text: {
    fontWeight: '500',
    fontSize: 16,
    lineHeight: 22,
    width: '100%',
  },
  boldText: {
    fontWeight: '600',
    fontSize: 16,
    lineHeight: 22,
    color: '#003353',
  },
  view3: {
    flexShrink: 1,
  },
  view4: {
    marginTop: 12,
    flexDirection: 'row',
  },
  linkText: {
    color: colors.accentText,
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 22,
    marginLeft: 15,
  },
  mobileFont: {
    fontSize: 14,
  },
})

export const SubmitSuccessAlert = observer(() => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const navigation = useNavigation()

  const { successAlertData, setSuccessAlertData } = ApplicationStore

  const onClick = () => {
    setSuccessAlertData({})
    navigation.navigate(paths.Console._self)
  }

  console.log('successAlertData', successAlertData)

  return (
    <Alert
      visible={!!successAlertData.message}
      onClose={onClick}
      title={t(successAlertData.title)}
      width={sm ? 650 : 356}
      height={sm ? 450 : 402}
      type={'success'}
      buttons={[
        {
          label: '',
          onPress: () => {},
        },
        {
          label: t('SubmitSuccessButton'),
          onPress: onClick,
          style: {
            width: sm ? 224 : 148,
          },
        },
      ]}
      icon={<SuccessGreenCheck style={{ marginBottom: 30 }} />}
    >
      <Spacer height={4} />
      <BtText
        style={composeStyle(
          sm ? submitAlertStyles.desktopText : submitAlertStyles.mobileText,
          submitAlertStyles.alertTextCommonStyle,
        )}
      >
        {t(successAlertData.message)}
      </BtText>
      <Spacer height={25} />
    </Alert>
  )
})

const submitAlertStyles = StyleSheet.create({
  desktopText: {
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 26,
    width: 470,
  },
  mobileText: {
    fontWeight: '400',
    fontSize: 16,
    lineHeight: 24,
    width: 292,
  },
  alertTextCommonStyle: {
    color: '#003353',
    textAlign: 'center',
  },
})

interface LineOfCreditAgreementAlertProps {
  agreementAlert: boolean
  onClose: () => void
  generating: boolean
  agree: () => void
  onLink: () => void
}

const LineOfCreditAgreementAlert: React.FC<LineOfCreditAgreementAlertProps> =
  observer(({ agreementAlert, onClose, generating, agree, onLink }) => {
    const { sm } = useResponsive()
    const { t } = useTranslation('application')

    const isLoCAccepted = useMemo(
      // keep this in memo to avoid sudden alert text change on fetchUser
      () => !!RootStore.userStore.credit?.LoCnumber,
      [],
    )

    return (
      <Alert
        visible={agreementAlert}
        onClose={onClose}
        noCloseIcon={generating}
        title={t('LoCAgreementAlert.Title')}
        buttons={[
          {
            label: '',
            onPress: () => {},
          },
          {
            label: t('LoCAgreementAlert.Agree&Continue'),
            onPress: agree,
            disabled: generating,
            testID: 'agree',
          },
        ]}
        icon={
          <FolderIcon
            style={{
              marginBottom: sm ? 33 : 18,
              marginTop: generating ? 20 : undefined,
            }}
          />
        }
        buttonsContainerStyle={sm ? styles.buttonsContainer : undefined}
      >
        {sm && <Spacer height={16} />}
        <BtText
          style={composeStyle(
            sm ? LoCstyles.desktopAlertText : styles.mobileAlertText,
            LoCstyles.alertTextCommonStyle,
          )}
        >
          <Trans
            t={t}
            i18nKey={
              isLoCAccepted
                ? 'LoCAgreementAlert.UpdatedAgreementMessage'
                : 'LoCAgreementAlert.NewAgreementMessage'
            }
            values={{
              application: ApplicationStore.isCreditApplication
                ? t('CreditRequest').toLowerCase()
                : t('GetPaidApplication').toLowerCase(),
            }}
            components={{
              bold: <Text style={{ fontWeight: '700' }} />,
              agreementLink: <Text style={LoCstyles.link} onPress={onLink} />,
            }}
          />
          {generating ? <ActivityIndicator style={{ marginLeft: 10 }} /> : null}
        </BtText>
        {sm && <Spacer height={25} />}
      </Alert>
    )
  })

interface LoCAgreementConfirmCloseProps {
  close: boolean
  setClose: (arg: boolean) => void
  setAgreementAlert: (arg: boolean) => void
}

const LoCAgreementConfirmClose: React.FC<LoCAgreementConfirmCloseProps> = ({
  close,
  setClose,
  setAgreementAlert,
}) => {
  const navigation = useNavigation()
  const { sm } = useResponsive()
  const { t } = useTranslation('application')
  return (
    <Alert
      visible={close}
      noCloseIcon
      title={t('LoCAgreementAlertClose.Title')}
      buttons={[
        {
          label: t('LoCAgreementAlertClose.GoToHome'),
          onPress: () => {
            setClose(!close)
            navigation.navigate(paths.Console._self)
          },
        },
        {
          label: t('LoCAgreementAlertClose.GoBack'),
          onPress: () => {
            setClose(!close)
            setAgreementAlert(true)
          },
          testID: 'goBack',
        },
      ]}
      icon={
        <WarningWithCircle
          style={{ marginBottom: sm ? 32 : 10, marginTop: 30 }}
        />
      }
      height={sm ? 430 : 402}
      buttonsContainerStyle={sm ? LoCstyles.buttonsContainer : undefined}
      verticalButtons={!sm}
    >
      <Spacer height={sm ? 10 : 4} />
      <BtText
        style={composeStyle(
          sm ? LoCstyles.desktopConfirmMessage : styles.mobileAlertText,
          LoCstyles.alertTextCommonStyle,
        )}
      >
        <Trans t={t} i18nKey="LoCAgreementAlertClose.Message" />
      </BtText>
      <Spacer height={20} />
    </Alert>
  )
}

export const LineOfCreditAgreement = observer(() => {
  const { userStore } = useStore()
  const { setAgreementAlert, agreementAlert } = ApplicationStore
  const [close, setClose] = useState<boolean>(false)
  const [generating, setGenerating] = useState<boolean>(false)

  const navigation = useNavigation()

  const getAgreement = async () => {
    setGenerating(true)
    await ApplicationStore.previewMasterAgreement()
    setGenerating(false)
  }

  const agree = async () => {
    await ApplicationStore.submit(navigation)
    userStore.fetchUser()
    setAgreementAlert(false)
  }

  return (
    <>
      <LineOfCreditAgreementAlert
        onClose={() => {
          setAgreementAlert(!agreementAlert)
          setClose(true)
        }}
        agreementAlert={agreementAlert}
        agree={agree}
        onLink={getAgreement}
        generating={generating}
      />
      <LoCAgreementConfirmClose
        close={close}
        setClose={setClose}
        setAgreementAlert={setAgreementAlert}
      />
    </>
  )
})

const LoCstyles = StyleSheet.create({
  desktopAlertText: {
    fontWeight: '500',
    fontSize: 18,
    width: 580,
  },
  alertTextCommonStyle: {
    lineHeight: 24,
    color: '#003353',
    textAlign: 'center',
  },
  link: {
    color: '#00A0F3',
    textDecorationLine: 'underline',
  },
  desktopConfirmMessage: {
    color: '#003353',
    fontWeight: '500',
    fontSize: 18,
    lineHeight: 24,
    width: 515,
  },
  buttonsContainer: { width: '85%', alignSelf: 'center' },
})
