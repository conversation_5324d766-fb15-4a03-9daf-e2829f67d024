import { getBranding } from '../../services/branding.service'
import { getAdminUrl, getApiURL } from '../../helpers/common'
import { emailService } from '../../services/email.service'
import { sendInvoicePaymentNotification } from '../../services/invoices.service/notifications'
import Sms from '../../services/sms.service'
import { rejectPayment } from '../../services/lms.service'
import {
  handleAchReversalOrReturn,
  handlePaymentOrderCancel,
} from '../../services/ledger/ledger.service'
import {
  CustomerAccount,
  Invoice,
  LoanApplication,
  Operation,
  Transaction,
} from '../../models'
import { IOperation } from '../../models/types'
import { dictionaries, exceptions } from '@linqpal/models'
import moment from 'moment-timezone'
import { isFactoringInvoice } from '../../services/payment/invoicePostPaymentActions.service'
import EmailBuilder from '../../helpers/EmailBuilder'
import SmsBuilder from '../../helpers/SmsBuilder'
import { Logger } from '../logger/logger.service'

const { OPERATION_STATUS, OPERATION_TYPES, TRANSACTION_STATUS } = dictionaries

const logger = new Logger({ module: 'cbw', subModule: 'processAch' })

export async function handleAchReturn(
  Original_Transaction_Number: string,
  Transaction_Number?: string,
  Transaction_Type?: string,
) {
  const log = logger.startTransaction()
  log.info('Started processing ACH return')
  const transactions = await Transaction.find({
    'metadata.transactionNumber': Original_Transaction_Number,
  })
  if (transactions.length) {
    for (const transaction of transactions) {
      const transactionNumber =
        Transaction_Number ??
        transaction.metadata.statusData.api?.originalReference ??
        ''
      const op = await Operation.findById(transaction.operation_id)
      if (op) {
        const invoice = await Invoice.findById(op.owner_id)

        switch (op.type) {
          case OPERATION_TYPES.ACH.PAYMENT:
            const emailMessage = EmailBuilder.getSubjectAndBody({
              key: 'processAchReturnAchPayment',
              data: {
                invoiceNumber: invoice?.invoice_number || '',
                operationAmount: op.amount,
              },
            })

            const html = `<div>${emailMessage.body}</div>`
            await notifyOperations(emailMessage.subject, html)
            break
          case OPERATION_TYPES.LOAN.REPAYMENT:
            await cancelLoanPayment(op)
            await notifyCustomer(op)
            break
        }
        op.status = OPERATION_STATUS.FAIL
        await op.save()
        transaction.status = TRANSACTION_STATUS.CANCELED
        await transaction.save()

        if (!invoice || !isFactoringInvoice(invoice)) {
          await sendInvoicePaymentNotification(op.owner_id, op.status)
        }
        log.info('Finished processing ACH return')

        await handleAchReversalOrReturn(
          transaction,
          transactionNumber,
          Transaction_Type ?? 'ACH_PULL_RETURN',
        )

        if (op.type === OPERATION_TYPES.ACH.PAYMENT) {
          await handlePaymentOrderCancel(transaction, transactionNumber)
        }
      }
    }
  }
}

async function cancelLoanPayment(operation: IOperation) {
  const log = logger.startTransaction()
  const loan = await LoanApplication.findById(operation.owner_id).populate(
    'company',
  )
  if (!loan) throw new exceptions.LogicalError('Loan not found')
  if (loan.metadata) {
    loan.metadata.payment_cancelled = true
    loan.metadata.payment_cancelled_by = 'SYSTEM'
    loan.metadata.payment_cancelled_date = moment().toDate()

    loan.markModified('metadata')
  } else {
    loan.metadata = {
      payment_cancelled: true,
      payment_cancelled_by: 'SYSTEM',
      payment_cancelled_date: moment().toDate(),
    }
  }
  await loan.save()
  const emailMessage = EmailBuilder.getSubjectAndBody({
    key: 'loanRepaymentCancelLoanPayment',
    data: {
      name: loan.company?.name,
      url: `${getAdminUrl()}/loan/status/${loan._id}`,
    },
  })

  await notifyOperations(emailMessage.subject, emailMessage.body)

  log.info(
    {
      paymentId: operation.metadata.lms_paymentId,
      paymentDate: operation.metadata.paymentDate,
    },
    'Started rejecting loan payment in LMS',
  )

  if (operation.metadata.paymentDate && operation.metadata.lms_paymentId) {
    try {
      await rejectPayment(operation.metadata.lms_paymentId)
    } catch (e) {
      log.info(
        {
          paymentId: operation.metadata.lms_paymentId,
          paymentDate: operation.metadata.paymentDate,
        },
        `Canceling in LMS failed ${e}`,
      )
    }
  }
  log.info(
    {
      paymentId: operation.metadata.lms_paymentId,
      paymentDate: operation.metadata.paymentDate,
    },
    'Finished rejecting loan payment in LMS',
  )
}

async function notifyOperations(subject: string, html: string) {
  const to =
    process.env.LP_MODE === 'prod'
      ? emailService.EMAILS.PRODUCT_OPERATION_TEAM?.split(',')
      : emailService.EMAILS.PRODUCT_OPERATION_TEAM_TEST?.split(',')

  await emailService.send({ to, subject, html })
}

async function notifyCustomer(operation: IOperation) {
  const app = await LoanApplication.findById(operation.owner_id)
  if (!app || !app.invoiceDetails?.invoiceId?.length) return
  const invoice = await Invoice.findById(app.invoiceDetails.invoiceId[0])
  const b = await getBranding({ customerId: app.company_id })
  const url = b ? b.host : getApiURL()
  const customer = invoice?.customer_account_id
    ? await CustomerAccount.findById(invoice.customer_account_id)
    : null
  if (!invoice || !customer) return
  const smsMessage = SmsBuilder.getMessage({
    key: 'loanRepaymentNotifyCustomer',
    data: { url },
  })
  const emailMessage = EmailBuilder.getSubjectAndBody({
    key: 'loanRepaymentNotifyCustomer',
    data: { url },
  })

  logger.info(
    `sending loan repayment notification to customer, phone ${customer.phone}, email ${customer.email}`,
  )

  await Promise.all([
    customer.phone ? Sms.send(customer.phone, smsMessage) : null,
    customer.email
      ? emailService.send({
          to: customer.email,
          subject: emailMessage.subject,
          html: `<p>${emailMessage.body}</p>`,
        })
      : null,
  ])
}
