import React, { FC } from 'react'
import ApplicationStore from '../../../../GeneralApplication/Application/ApplicationStore'
import { BtButton } from '@linqpal/components/src/ui'
import { useTranslation } from 'react-i18next'
import { useResponsive } from '../../../../../../utils/hooks'

interface AddCoOwnerButtonProps {
  onPress: () => void
}

export const AddCoOwnerButton: FC<AddCoOwnerButtonProps> = ({ onPress }) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()

  return (
    <>
      {ApplicationStore.canHaveCoOwners ? (
        <BtButton
          size="small"
          appearance="ghost"
          status="basic"
          onPress={onPress}
          style={
            sm
              ? { marginLeft: 'auto' }
              : { marginTop: 20, width: 180, alignSelf: 'center' }
          }
          // eslint-disable-next-line i18next/no-literal-string
          iconLeft="plus-outline"
        >
          {t('CoOwners.AddCoOwner')}
        </BtButton>
      ) : null}
    </>
  )
}
