import { createTransaction } from './transaction'
import { Company, CardPricingPackage } from '../../models'
import { getTabapayFailureReason } from './helpers'
import mongoose from 'mongoose'
import { IInvoice, ITransaction } from '../../models/types'
import { ITransactionResponse } from './types'

export const processTransaction = async (
  transactions: ITransaction[],
  transactionType: 'PUSH' | 'PULL',
  sourceAccountId: string,
  destinationAccountId: string,
  amount: number,
  tax_amount: number,
  fee: number,
  supplierId: string,
  session: mongoose.ClientSession,
  invoice?: IInvoice, // added for better logging
): Promise<{
  success: boolean
  tabapayResponse?: ITransactionResponse
  failureReason?: string
}> => {
  console.log('processing card transaction')

  const supplierCompany = await Company.findById(supplierId)
  const cardPricingPackage = await CardPricingPackage.findOne({
    name: supplierCompany?.settings?.cardPricingPackageId,
  })

  // create pull transaction
  const createTransactionResponse = await createTransaction(
    transactionType,
    amount,
    tax_amount,
    fee,
    sourceAccountId,
    destinationAccountId,
    supplierCompany!,
    cardPricingPackage!.tabapayMID,
  )

  console.log(
    `createTransactionResponse for ${invoice?._id}: `,
    createTransactionResponse,
  )

  const failureReason = getTabapayFailureReason(createTransactionResponse)
  if (failureReason) {
    return {
      success: false,
      tabapayResponse: createTransactionResponse,
      failureReason,
    }
  }

  console.log(
    `adding ${transactionType} transaction ${createTransactionResponse.transactionID} to DB`,
  )

  console.log(
    `successfully added ${transactionType} transaction ${createTransactionResponse.transactionID} to DB`,
  )

  const transactionObject: any = {
    transactionNumber: createTransactionResponse?.transactionID,
    transactionID: createTransactionResponse?.transactionID,
    network: createTransactionResponse?.network,
    networkRC: createTransactionResponse?.networkRC,
    networkID: createTransactionResponse?.networkID,
    approvalCode: createTransactionResponse?.approvalCode,
    sourceAccountId: sourceAccountId,
    destinationAccountId: destinationAccountId,
  }

  if (createTransactionResponse?.AVS) {
    transactionObject.avsResponseCode = createTransactionResponse?.AVS.codeAVS
    transactionObject.securityCodeResponseCode =
      createTransactionResponse?.AVS.codeSecurityCode
  }
  // if (createTransactionResponse?.fees) {
  //   transactionObject.interchangeFees =
  //     createTransactionResponse?.fees.interchange
  //   transactionObject.networkFees = createTransactionResponse?.fees.network
  //   transactionObject.tabapayFees = createTransactionResponse?.fees.tabapay
  // }
  if (createTransactionResponse?.card) {
    transactionObject.cardLastFour = createTransactionResponse?.card.last4
    transactionObject.cardExpirationDate =
      createTransactionResponse?.card.expirationDate
  }

  for (const transaction of transactions) {
    await transaction.updateOne(
      {
        metadata: {
          ...transaction.metadata,
          ...transactionObject,
        },
      },
      { session },
    )
  }

  return {
    success: true,
    tabapayResponse: createTransactionResponse,
  }
}
