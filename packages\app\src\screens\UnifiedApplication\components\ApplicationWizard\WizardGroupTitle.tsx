import { useResponsive } from '../../../../utils/hooks'
import { StyleSheet, View } from 'react-native'
import { BtPlainText } from '@linqpal/components/src/ui'
import React, { FC } from 'react'
import { ProgressBar } from '../../../GeneralApplication/Application/components'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import { getGroupTitle } from '../../../GeneralApplication/Application/groupTitles'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react-lite'
import { colors } from '@linqpal/components/src/theme'

// TODO: VK: Unified: calculate progress

export const WizardGroupTitle: FC = observer(() => {
  const { sm } = useResponsive()
  const { t } = useTranslation('application')

  const store = useUnifiedApplication()

  if (!store.editorOptions.showGroupTitle) return null

  const titleKey = getGroupTitle(store.currentGroup)
  const title = t(titleKey as any)

  return title && sm ? (
    <>
      <View style={styles.titleWrapper}>
        <BtPlainText style={styles.title} testID="ApplicationTitle">
          {title}
        </BtPlainText>
      </View>
      <ProgressBar progress={0} />
    </>
  ) : null
})

const styles = StyleSheet.create({
  titleWrapper: {
    padding: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: colors.accentText,
  },
})
