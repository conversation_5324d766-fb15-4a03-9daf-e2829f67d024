import { makeAutoObservable } from 'mobx'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import {
  validateDate,
  validateEmail,
  validateSsn,
  validateUSPhoneNumber,
  validateZip,
} from '@linqpal/models/src/helpers/validations'
import { MIN_COOWNER_PERCENTAGE } from '../../../../GeneralApplication/Application/ApplicationStore'

export class CoOwner {
  // common
  id: string = ''
  type: typeof OwnerTypes.INDIVIDUAL | typeof OwnerTypes.ENTITY = OwnerTypes.INDIVIDUAL
  percentOwned: number = 0
  address: string = ''
  city: string = ''
  state: string = ''
  zip: string = ''
  phone: string = ''
  email: string = ''
  // individual owner
  firstName: string = ''
  lastName: string = ''
  birthday: string = ''
  ssn: string = ''
  // entity
  entityName: string = ''
  ein: string = ''
  key: string = ''

  constructor(data?: Partial<CoOwner>) {
    if (data) {
      Object.assign(this, data)
    }
    makeAutoObservable(this)
  }

  get isFilled(): boolean {
    const requiredFields = [
      this.firstName,
      this.lastName,
      this.percentOwned,
      this.address,
      this.city,
      this.state,
      this.zip,
      this.phone,
      this.email,
    ].concat(
      this.type === OwnerTypes.INDIVIDUAL
        ? [this.birthday, this.ssn]
        : [this.entityName, this.ein],
    )

    const allRequiredFilled = requiredFields.every((value) => value)

    const allIndividualFieldsValid =
      this.type === OwnerTypes.INDIVIDUAL &&
      validateSsn(this.ssn) &&
      validateDate(this.birthday, 1900)

    const allEntityFieldsValid =
      this.type === OwnerTypes.ENTITY && validateSsn(this.ein)

    const isValidPercentage =
      MIN_COOWNER_PERCENTAGE <= this.percentOwned && this.percentOwned <= 100

    return (
      isValidPercentage &&
      allRequiredFilled &&
      validateEmail(this.email) &&
      validateUSPhoneNumber(this.phone) &&
      validateZip(this.zip) &&
      (allIndividualFieldsValid || allEntityFieldsValid)
    )
  }

  // common actions
  setType(type: typeof OwnerTypes.INDIVIDUAL | typeof OwnerTypes.ENTITY) {
    if (type === OwnerTypes.ENTITY) {
      this.firstName = ''
      this.lastName = ''
      this.birthday = ''
      this.ssn = ''
    } else {
      this.entityName = ''
      this.ein = ''
    }
    this.type = type
  }

  setPercentage(percentage: number) {
    this.percentOwned = percentage
  }

  setPhone(phone: string) {
    this.phone = phone
  }

  setEmail(email: string) {
    this.email = email
  }

  // individual owner actions
  setFirstName(firstName: string) {
    this.firstName = firstName
  }

  setLastName(lastName: string) {
    this.lastName = lastName
  }

  setBirthday(birthday: string) {
    this.birthday = birthday
  }

  setSSN(ssn: string) {
    this.ssn = ssn
  }

  // entity owner actions
  setEntityName(name: string) {
    this.entityName = name
  }

  setEIN(ein: string) {
    this.ein = ein
  }

  setKey(key: string) {
    this.key = key
  }

  // Method needed for AddressEditor compatibility
  setValue(field: string, value: any) {
    switch (field) {
      case 'address':
        this.address = value
        break
      case 'city':
        this.city = value
        break
      case 'state':
        this.state = value
        break
      case 'zip':
        this.zip = value
        break
      default:
        // Handle other fields if needed
        break
    }
  }
}
