import React, { FC, useEffect } from 'react'
import { BtButton, BtText } from '@linqpal/components/src/ui'
import { observer } from 'mobx-react'
import { StyleSheet, View } from 'react-native'
import { useTranslation } from 'react-i18next'
import { FlowController, ModelUpdateInfo } from '../../FlowController'
import { Divider } from '@ui-kitten/components'
import ApplicationStore from '../../ApplicationStore'
import { getSnapshot } from 'mobx-state-tree'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { OwnerIcon } from '../../components/OwnerIcon'
import { CoOwner, EditableCoOwnerModel } from './components/CoOwner'
import { CoOwnerEditor } from './components/CoOwnerEditor'
import { CoOwnersReview } from '../review/components/CoOwnersReview'
import { useCoOwnerStore } from './components/CoOwnersStore'
import { useResponsive } from '../../../../../utils/hooks'
import { AddCoOwnerButton } from './components/AddCoOwnerButton'
import { v4 } from 'uuid'

interface CoOwnersProps {
  doc: any
  flowController: FlowController
  onValueUpdate: (updateInfo: ModelUpdateInfo) => void
  setNavigationVisible: (visible: boolean) => void
  review: boolean
}

const CoOwners: FC<CoOwnersProps> = ({
  doc,
  flowController,
  onValueUpdate,
  setNavigationVisible,
  review,
}) => {
  const { t } = useTranslation('application')
  const { sm } = useResponsive()
  const store = useCoOwnerStore()

  useEffect(() => {
    store.fill(doc)

    // force onValueUpdate to update buttons status in host Editor
    raiseValueUpdate()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [doc])

  useEffect(() => {
    if (!setNavigationVisible) return
    if (ApplicationStore.currentCoOwnerIndex < 0) {
      setNavigationVisible(true)
    } else {
      setNavigationVisible(false)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [ApplicationStore.currentCoOwnerIndex])

  const createNew = () => {
    startEdit(store.coOwnersCount)
  }

  const remove = (index: number) => {
    store.removeAt(index)
    store.updateDraft()

    raiseValueUpdate()
  }

  const createEditableCoOwner = () => {
    const coOwner = store.get(ApplicationStore.currentCoOwnerIndex)
    const snapshot = coOwner ? getSnapshot(coOwner) : { id: v4() }

    return EditableCoOwnerModel.create(snapshot)
  }

  const startEdit = (index: number) => {
    ApplicationStore.setCurrentCoOwnerIndex(index)
    setNavigationVisible(false)
  }

  const finishEdit = (coOwner: CoOwner) => {
    if (ApplicationStore.currentCoOwnerIndex === store.coOwnersCount) {
      store.add(coOwner)
    } else {
      store.updateAt(ApplicationStore.currentCoOwnerIndex, coOwner)
    }

    store.updateDraft()

    ApplicationStore.setCurrentCoOwnerIndex(-1)
    ApplicationStore.tryGoBackToReview()

    setNavigationVisible(true)
    raiseValueUpdate()
  }

  const cancelEdit = () => {
    ApplicationStore.setCurrentCoOwnerIndex(-1)
    ApplicationStore.tryGoBackToReview()

    setNavigationVisible(true)
  }

  const raiseValueUpdate = () =>
    // notify host editor to update navigation buttons state
    onValueUpdate({
      value: store.hasCoOwners,
      filled: store.isFilled,
      group: 'coOwnerInfo',
      identifier: 'coOwners',
    })

  return (
    <>
      {review ? (
        <CoOwnersReview
          coOwners={store.coOwners}
          doc={doc}
          flowController={flowController}
        />
      ) : (
        <>
          {ApplicationStore.currentCoOwnerIndex < 0 && (
            <>
              {store.coOwners.map((coOwner, index) => (
                <>
                  <View style={styles.coOwnersList} key={coOwner.key}>
                    <OwnerIcon isOwnerFilled={coOwner.isFilled} />
                    <View style={styles.coOwnerDetails}>
                      <BtText
                        style={styles.coOwnerType}
                        onPress={() => startEdit(index)}
                      >
                        {coOwner.type === OwnerTypes.INDIVIDUAL
                          ? t('CoOwners.IndividualOwner')
                          : t('CoOwners.EntityOwner')}
                      </BtText>
                      {coOwner.firstName ||
                      coOwner.lastName ||
                      coOwner.entityName ? (
                        <BtText
                          style={styles.ownerName}
                          onPress={() => startEdit(index)}
                        >
                          {coOwner.type === OwnerTypes.INDIVIDUAL
                            ? `${coOwner.firstName} ${coOwner.lastName}`
                            : coOwner.entityName}
                        </BtText>
                      ) : null}
                      <BtText
                        style={styles.coOwnerPercentage}
                        onPress={() => startEdit(index)}
                      >{`${coOwner.percentOwned}% ${t(
                        'CoOwners.OwnerPercentagePostfix',
                      )}`}</BtText>
                    </View>
                    <View style={styles.coOwnerButtons}>
                      <BtButton
                        appearance={'ghost'}
                        size={'small'}
                        status={'basic'}
                        onPress={() => startEdit(index)}
                        // eslint-disable-next-line i18next/no-literal-string
                        iconLeft="edit-outline"
                      >
                        {t('Review.Edit')}
                      </BtButton>
                      <BtButton
                        size="small"
                        appearance={'ghost'}
                        status={'basic'}
                        onPress={() => remove(index)}
                        // eslint-disable-next-line i18next/no-literal-string
                        iconLeft="trash-2-outline"
                      >
                        {t('Bank.DeleteManualButton')}
                      </BtButton>
                    </View>
                  </View>
                  <Divider />
                </>
              ))}
              <View style={styles.footer}>
                <View style={styles.totalPercentageContainer}>
                  <BtText style={styles.percentageAmount}>
                    {t('CoOwners.TotalPercentage')}
                  </BtText>
                  <BtText
                    size={16}
                    color={
                      ApplicationStore.totalOwnershipPercentage > 100
                        ? '#DB081C'
                        : ''
                    }
                  >
                    {ApplicationStore.totalOwnershipPercentage}%
                  </BtText>
                </View>
                {sm && <AddCoOwnerButton onPress={() => createNew()} />}
              </View>
              <Divider />
              {!sm && <AddCoOwnerButton onPress={() => createNew()} />}
            </>
          )}
          {ApplicationStore.currentCoOwnerIndex >= 0 && (
            <CoOwnerEditor
              coOwner={createEditableCoOwner()}
              onSave={finishEdit}
              onCancel={cancelEdit}
            />
          )}
        </>
      )}
    </>
  )
}

const styles = StyleSheet.create({
  coOwnersList: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 15,
  },
  coOwnerDetails: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    marginLeft: 10,
    marginTop: 10,
  },
  footer: {
    flexDirection: 'row',
    paddingTop: 20,
    paddingBottom: 25,
  },
  coOwnerType: {
    fontSize: 14,
    fontWeight: '400',
    color: 'gray',
  },
  ownerName: {
    fontSize: 16,
    fontWeight: '700',
    marginTop: 5,
  },
  coOwnerPercentage: {
    fontSize: 16,
    fontWeight: '500',
    marginTop: 5,
    marginBottom: 10,
  },
  coOwnerButtons: {
    flexDirection: 'row',
    marginLeft: 'auto',
  },
  totalPercentageContainer: {
    flexDirection: 'row',
    marginTop: 5,
  },
  percentageAmount: {
    fontSize: 16,
    fontWeight: '700',
  },
})

// noinspection JSUnusedGlobalSymbols
export default {
  title: () =>
    ApplicationStore.currentCoOwnerIndex < 0
      ? 'CoOwners.Title'
      : 'CoOwners.CoOwnerDetailsTitle',
  description: '',
  component: observer(CoOwners),
  handleBack: () => {
    if (ApplicationStore.currentCoOwnerIndex >= 0) {
      ApplicationStore.setCurrentCoOwnerIndex(-1)
      ApplicationStore.tryGoBackToReview()
      return true
    } else {
      return false
    }
  },
}
