import { makeAutoObservable } from 'mobx'
import { createContext, useContext } from 'react'
import RootStore from '../../../../../../store/RootStore'
import { IDraftModel } from '@linqpal/models'
import { CoOwner } from './CoOwner'
import ApplicationStore from '../../../../GeneralApplication/Application/ApplicationStore'

export class CoOwnersStore {
  coOwners: CoOwner[] = []

  constructor() {
    makeAutoObservable(this)
  }

  get isFilled(): boolean {
    const allEmailsUnique =
      new Set(this.coOwners.map((c) => c.email)).size === this.coOwners.length

    const allCoOwnersFilled =
      this.coOwners.length === 0 || this.coOwners.every((c) => c.isFilled)

    return (
      allCoOwnersFilled &&
      allEmailsUnique &&
      ApplicationStore.totalOwnershipPercentage <= 100
    )
  }

  get hasCoOwners(): boolean {
    return this.coOwners.length > 0
  }

  get coOwnersCount(): number {
    return this.coOwners.length
  }

  get(index: number): CoOwner | undefined {
    return this.coOwners[index]
  }

  fill(doc: IDraftModel) {
    this.coOwners = []
    const draftCoOwners = [1, 2, 3, 4] // can have up to 4 co-owners, 25% each
      .map((index) => doc?.get('coOwnerInfo', `coOwner${index}`))
      .filter((item) => item)
    draftCoOwners.forEach((item) => this.add(item))
  }

  add(coOwnerData: any): number {
    const coOwner = new CoOwner(coOwnerData)
    this.coOwners.push(coOwner)

    const newIndex = this.coOwners.length - 1
    this.coOwners[newIndex].key = newIndex + new Date().getTime().toString()

    return newIndex
  }

  removeAt(index: number) {
    this.coOwners.splice(index, 1)
  }

  updateAt(index: number, coOwner: CoOwner) {
    this.coOwners[index] = coOwner
  }

  updateDraft() {
    const draft = RootStore.userStore.document

    // use coOwners field to save Filled property to let navigation controls / review page work
    draft?.setContent(
      'coOwnerInfo',
      `coOwners`,
      this.hasCoOwners,
      this.isFilled,
    )

    // remove draft co-owners which have been removed from store,
    // 4 is the maximum number of co-owners with 25% each
    for (let i = this.coOwners.length + 1; i <= 4; i++) {
      draft?.clearItems([`coOwnerInfo.coOwner${i}`])
    }

    // rewrite co-owners in draft with co-owners from store
    for (let i = 0; i < this.coOwners.length; i++) {
      const coOwner = this.coOwners[i]
      const snapshot = {
        id: coOwner.id,
        type: coOwner.type,
        percentOwned: coOwner.percentOwned,
        address: coOwner.address,
        city: coOwner.city,
        state: coOwner.state,
        zip: coOwner.zip,
        phone: coOwner.phone,
        email: coOwner.email,
        firstName: coOwner.firstName,
        lastName: coOwner.lastName,
        birthday: coOwner.birthday,
        ssn: coOwner.ssn,
        entityName: coOwner.entityName,
        ein: coOwner.ein,
      }

      draft?.setContent(
        'coOwnerInfo',
        `coOwner${i + 1}`,
        snapshot,
        coOwner.isFilled,
      )
    }
  }
}

const CoOwnersContext = createContext<CoOwnersStore>(new CoOwnersStore())

export function useCoOwnerStore() {
  return useContext(CoOwnersContext)
}
