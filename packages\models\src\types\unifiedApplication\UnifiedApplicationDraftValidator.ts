import {
  IUnifiedApplicationDraft,
  IUnifiedApplicationOptions,
} from './IUnifiedApplicationDraft'
import { validateEmail } from '../../helpers/validations'

// we'll potentially have dynamic structures in the future, so for initial version using straightforward validation
// later it can be replaced partially with class-validator or something like this

export class UnifiedApplicationDraftValidator {
  private draft: IUnifiedApplicationDraft

  constructor(draft: IUnifiedApplicationDraft) {
    this.draft = draft
  }

  validate(path: string, options: IUnifiedApplicationOptions): boolean {
    console.log('options', options)

    switch (path) {
      // businessInfo group
      case 'businessInfo.email':
        return validateEmail(this.draft.data?.businessInfo?.email)
      case 'businessInfo.category':
        // done
        return !!this.draft.data?.businessInfo?.category
      case 'businessInfo.businessName':
        // done
        return !!this.draft.data?.businessInfo?.businessName?.legalName
      case 'businessInfo.trade':
        return !!this.draft.data?.businessInfo.trade
      case 'businessInfo.businessPhone':
        return !!this.draft.data?.businessInfo.businessPhone
      case 'businessInfo.businessAddress':
        return !!this.draft.data?.businessInfo.businessAddress
      case 'businessInfo.startDate':
        return !!this.draft.data?.businessInfo.startDate
      case 'businessInfo.type':
        return !!this.draft.data?.businessInfo.type
      case 'businessInfo.ein':
        return !!this.draft.data?.businessInfo.ein

      // finance group
      case 'finance.revenue':
        // optional for IHC
        return !!this.draft.data?.finance.revenue
      case 'finance.debt':
        return !!this.draft.data?.finance.debt
      case 'finance.howMuchCredit':
        return !!this.draft.data?.finance.howMuchCredit
      case 'finance.arAdvanceRequestedLimit':
        // optional for  Get Paid
        return !!this.draft.data?.finance.arAdvanceRequestedLimit

      // businessOwner group
      case 'businessOwner.isOwner':
        return !!this.draft.data?.businessOwner.isOwner
      case 'businessOwner.isAuthorized':
        return !!this.draft.data?.businessOwner.isAuthorized
      case 'businessOwner.authorizedDetails':
        return !!this.draft.data?.businessOwner.authorizedDetails
      case 'businessOwner.address':
        // optional for non-owner non-authorized
        return !!this.draft.data?.businessOwner.address
      case 'businessOwner.birthdate':
        // optional for non-owner non-authorized
        return !!this.draft.data?.businessOwner.birthdate
      case 'businessOwner.ssn':
        // optional for non-owner non-authorized
        return !!this.draft.data?.businessOwner.ssn

      // coOwnerInfo group
      case 'coOwnerInfo.coOwners':
        return !!this.draft.data?.coOwnerInfo.coOwners

      // bank group
      case 'bank.details':
        // optional for IHC, see appStore.isOptionalStep for nuances
        return !!this.draft.data?.bank.details

      // TODO: VK: Unified: remove preview and agreement steps, to be moved to a single review step
      // review group
      case 'review.review':
        return true
      case 'review.preview':
        return true
      case 'review.agreement':
        return true

      default:
        throw new Error(`Unknown validation path: ${path}`)
    }
  }
}
