import { ApplicationType } from '../../dictionaries/applicationType'

export interface IUnifiedApplicationOptions {
  type: ApplicationType
  userId: string
}

export interface IUnifiedApplicationDraft {
  initialStep: string
  currentStep: string
  data: IUnifiedApplicationData
}

export interface IUnifiedApplicationData {
  businessInfo: {
    email?: string
    category?: string
    businessName?: {
      legalName?: string
      dba?: string
    }
    trade?: string
    businessPhone?: string
    businessAddress?: string
    startDate?: string
    type?: string
    ein?: string
  }
  finance: {
    revenue?: string
    debt?: string
    howMuchCredit?: string
    arAdvanceRequestedLimit?: string
  }
  businessOwner: {
    isOwner?: string
    ownershipPercentage?: string
    isAuthorized?: string
    authorizedDetails?: string
    address?: string
    birthdate?: string
    ssn?: string
  }
  coOwnerInfo: {
    coOwners?: string
  }
  bank: {
    details?: string
  }
  review: {
    review?: string
    preview?: string
    agreement?: string
  }
}
