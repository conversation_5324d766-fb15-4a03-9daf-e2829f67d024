import { makeAutoObservable } from 'mobx'
import { FlowController_v1 } from '../../GeneralApplication/Store/FlowController_v1'
import { UnifiedApplicationFlow } from '../../GeneralApplication/Store/UnifiedApplicationFlow'
import { routes2 } from '@linqpal/models'
import { UnifiedApplicationDraft } from './UniffiedApplicationDraft'
import { Steps } from '../../GeneralApplication/Store/ApplicationSteps'
import RootStore from '../../../store/RootStore'
import { ApplicationType } from '@linqpal/models/src/dictionaries/applicationType'
import { IApplicationStepOptions } from '../flow/ApplicationEditorSelector'
import { IUnifiedApplicationOptions } from '@linqpal/models/src/types/unifiedApplication/IUnifiedApplicationDraft'
import ApplicationStore from '../../GeneralApplication/Application/ApplicationStore'
import { UnifiedApplicationReviewStore } from './UnifiedApplicationReviewStore'

export class UnifiedApplicationStore {
  private _flow: UnifiedApplicationFlow = new UnifiedApplicationFlow()

  private _options: IUnifiedApplicationOptions

  private _document: UnifiedApplicationDraft

  private _flowController: FlowController_v1<
    UnifiedApplicationDraft,
    IUnifiedApplicationOptions
  >

  private _previousStep = ''

  private _editorOptions: IApplicationStepOptions = {}

  private _isSubmitting = false

  private _reviewStore = new UnifiedApplicationReviewStore()

  constructor() {
    this._flow = new UnifiedApplicationFlow()
    this._document = new UnifiedApplicationDraft()
    this._flowController = new FlowController_v1(this._flow.flow)

    this._options = {
      // TODO: VK: Unified: review type
      type: ApplicationType.Credit,
      userId: RootStore.userStore.id,
    }

    makeAutoObservable(this)
  }

  public get reviewStore() {
    return this._reviewStore
  }

  public get currentStep(): string {
    return this._document?.currentStep || ''
  }

  public get previousStep(): string {
    return this._previousStep
  }

  public get currentGroup(): string {
    const currentStep = this._document?.currentStep || ''
    const group = currentStep.split('.')[0] || ''

    return group
  }

  public get document() {
    return this._document
  }

  public get type(): ApplicationType {
    return this._options.type
  }

  public get isSupplierApp(): boolean {
    return this._options.type === ApplicationType.Supplier
  }

  public get isCreditApp(): boolean {
    return this._options.type === ApplicationType.Credit
  }

  public get isIhcApp(): boolean {
    return this._options.type === ApplicationType.InHouseCredit
  }

  public get isCurrentStepValid(): boolean {
    return this.currentStep
      ? this._document.validate(this.currentStep, this._options)
      : false
  }

  public get isInReview(): boolean {
    return this.currentGroup === Steps.review._
  }

  public get hasSubmissionRights(): boolean {
    // TODO: VK: Unified: re-implement hasSubmissionRights
    return 2 - 2 === 0 || ApplicationStore.hasSubmissionRights
  }

  public get canSubmit(): boolean {
    const steps = this.getFlowSteps()

    const allStepsValid = steps.every((step) =>
      this._document.validate(step, this._options),
    )

    const shouldReadAgreement = this.isSupplierApp || this.isCreditApp

    // TODO: VK: Unified: re-implement hasSubmissionRights here
    return (
      this.hasSubmissionRights &&
      allStepsValid &&
      this.reviewStore.isAgreementAccepted &&
      (!shouldReadAgreement || this.reviewStore.isAgreementRead)
    )
  }

  public get isSubmitting(): boolean {
    return this._isSubmitting
  }

  public get editorOptions(): IApplicationStepOptions {
    return this._editorOptions
  }

  public setEditorOptions(options: IApplicationStepOptions) {
    this._editorOptions = {
      ...options,
      canGoBack: options.canGoBack ?? true,
      canSkip: options.canSkip ?? true,
      showGroupTitle: options.showGroupTitle ?? true,
    }
  }

  public async loadDraft() {
    // TODO: VK: Unified: review type
    this._options = {
      type: ApplicationType.Credit,
      userId: RootStore.userStore.id,
    }

    const draftResponse = await routes2.application.getDraft({})

    if (draftResponse.draft) {
      this._document = new UnifiedApplicationDraft(draftResponse.draft)

      const flowSteps = this.getFlowSteps()

      if (!flowSteps.includes(this.currentStep)) {
        this._document.currentStep = Steps.review.review.path
        this._previousStep = Steps.review.review.path
      }
    } else {
      // TODO: VK: calculate initial steps as in old ApplicationStore
      const initialStep = Steps.businessInfo.email.path

      this._document = new UnifiedApplicationDraft({
        initialStep,
        currentStep: initialStep,
        data: {
          businessInfo: {
            businessName: {},
          },
          finance: {},
          businessOwner: {},
          coOwnerInfo: {},
          bank: {},
          review: {},
        },
      })

      console.log('new unified application, initial step', initialStep)
    }

    return Promise.resolve()
  }

  public async saveDraft() {
    // call on next / back / skip / close / submit. Enqueue save requests, check conflicts
    return Promise.resolve()
  }

  public async submitApplication() {
    this._isSubmitting = true
    // TODO: VK: Unified: do actual work
    this._isSubmitting = false

    return Promise.resolve()
  }

  public goToStep(path: string) {
    this._previousStep = this.currentStep
    this._document.currentStep = path
  }

  public moveNext() {
    const newStep = this._flowController.getNextStep(
      this.currentStep,
      this._document,
      this._options,
    )

    this.setCurrentStep(newStep)
  }

  public moveBack() {
    const newStep = this._flowController.getPreviousStep(
      this.currentStep,
      this._document,
      this._options,
    )

    this.setCurrentStep(newStep)
  }

  public skipStep() {
    const newStep = this._flowController.findSkipStep(
      this.currentStep,
      this._document,
      this._options,
    )

    this.setCurrentStep(newStep)
  }

  // TODO: VK: Unified: review if includeReview is needed
  public getFlowSteps(includeReview = false) {
    return this._flowController.getFlowSteps(
      this._document,
      this._options,
      includeReview,
    )
  }

  public getFlowGroups() {
    return this._flowController.getFlowGroups(this._document, this._options)
  }

  public getGroupSteps(group: string) {
    return this._flowController.getGroupSteps(
      group,
      this._document,
      this._options,
    )
  }

  public validateStep(step: string) {
    return this._document.validate(step, this._options)
  }

  public editGroup(group: string, fromBeginning = true) {
    const groupSteps = this.getGroupSteps(group)

    const firstInvalidIndex = groupSteps.findIndex(
      (step) => !this.validateStep(step),
    )

    const allValid = firstInvalidIndex === -1
    const noneValid = firstInvalidIndex === 0

    if (fromBeginning || noneValid || allValid) {
      const newStep = this._flowController.getFirstGroupStep(
        group,
        this._document,
        this._options,
      )
      this.setCurrentStep(newStep)
    } else {
      const newStep = groupSteps[firstInvalidIndex]
      this.setCurrentStep(newStep)
    }
  }

  private setCurrentStep(step: string) {
    this._previousStep = this.currentStep
    this._document.currentStep = step
  }
}
