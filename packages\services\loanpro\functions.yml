processDue:
  handler: index.processDue
  events:
    - sqs:
        arn: !GetAtt DueLoansFifoQueue.Arn
        enabled: false

getDue:
  handler: index.getDueLoans
  events:
    - schedule:
        rate: cron(5 13 * * ? *)
        enabled: false

btCloseLoan:
  handler: index.btCloseLoan
  events:
    - schedule:
        rate: cron(0 6 * * ? *)
        enabled: true

btFinalPayment:
  handler: index.btFinalPayment
  events:
    - schedule:
        rate: cron(0 6 * * ? *)
        enabled: true

syncLMSLoans:
  handler: index.syncLMSLoans
  events:
    - schedule:
        rate: cron(0/15 * ? * * *)
        enabled: true
