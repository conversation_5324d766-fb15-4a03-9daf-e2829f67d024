import axios, { AxiosInstance } from 'axios'
import {
  IAccountBody,
  IAccountInfoResponse,
  IAddAccountResponse,
  ITransactionBody,
  ITransactionResponse,
} from './types'

export class TabaPay {
  tabapayRequester: AxiosInstance

  constructor() {
    this.tabapayRequester = axios.create({
      baseURL:
        process.env.LP_MODE !== 'prod'
          ? 'https://api.sandbox.tabapay.net:10443'
          : 'https://api-use1.tabapay.net:10443',
      responseType: 'json',
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        Authorization: 'Bearer ' + process.env.LP_TABAPAY_BEARER_TOKEN,
      },
      timeout: process.env.NODE_ENV === 'development' ? 120000 : 10000,
    })
    this.tabapayRequester.interceptors.request.use((request) => {
      console.log('tabapay request url: ', request.url)
      console.log('tabapay request body: ', request.data)
      return request
    })
  }

  async getClient() {
    const response = await this.tabapayRequester.get(
      `/v1/clients/${process.env.LP_TABAPAY_CLIENT_ID}`,
    )
    return response.data
  }

  async addAccount(data: IAccountBody): Promise<IAddAccountResponse> {
    const resp = await this.tabapayRequester.post(
      `/v1/clients/${process.env.LP_TABAPAY_CLIENT_ID}/accounts`,
      data,
    )
    return {
      accountId: resp.data.accountID,
      cardLastFour: resp.data.card.last4,
      cardExpDate: resp.data.card.expirationDate,
      response: resp.data,
    }
  }

  async getAccountInfo(
    data: IAccountBody,
    withAVS = true,
  ): Promise<IAccountInfoResponse> {
    const { data: response } = await this.tabapayRequester.post(
      `/v1/clients/${process.env.LP_TABAPAY_CLIENT_ID}_${
        process.env.LP_TABAPAY_MID_NO_CONVENIENCE_FEE
      }/cards?${withAVS ? 'AVS' : ''}`,
      data,
    )
    return {
      isPullEnabled: response.card.pull.enabled,
      cardNetwork: response.card.pull.network,
      cardType: response.card.pull.type,
      isRegulated: response.card.pull.regulated,
      avsNetworkRC: response.AVS?.networkRC,
      avsAuthorizeID: response.AVS?.authorizeID,
      avsResultText: response.AVS?.resultText,
      avsCode: response.AVS?.codeAVS,
      avsSecurityCode: response.AVS?.codeSecurityCode,
      avsEC: response.AVS?.EC,
      binNumber: response.card.bin,
      response,
    }
  }

  async createTransaction(
    tabapayMID: string,
    data: ITransactionBody,
  ): Promise<ITransactionResponse> {
    const resp = await this.tabapayRequester.post(
      `/v1/clients/${process.env.LP_TABAPAY_CLIENT_ID}_${tabapayMID}/transactions`,
      data,
    )
    console.log('createTransaction response: ', resp.data)

    const {
      status,
      transactionID,
      network,
      networkRC,
      networkID,
      approvalCode,
      AVS,
      fees,
      card,
    } = resp.data
    return {
      status,
      transactionID,
      network,
      networkRC,
      networkID,
      approvalCode,
      AVS,
      fees,
      card,
    }
  }

  async revertTransaction(transactionId: string, amount?: number | string) {
    const resp = await this.tabapayRequester.delete(
      `/v1/clients/${process.env.LP_TABAPAY_CLIENT_ID}_${process.env.LP_TABAPAY_MID_NO_CONVENIENCE_FEE}/transactions/${transactionId}?reversal`,
      { data: { amount } },
    )
    return resp.data
  }
}
