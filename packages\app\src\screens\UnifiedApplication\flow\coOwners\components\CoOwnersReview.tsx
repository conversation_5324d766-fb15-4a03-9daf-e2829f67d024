import React, { FC } from 'react'
import { <PERSON>t<PERSON>utton, BtText } from '@linqpal/components/src/ui'
import { View } from 'react-native'
import { OwnerTypes } from '@linqpal/models/src/dictionaries/UnifiedApplication'
import { formatAddress } from '@linqpal/models/src/helpers/addressFormatter'
import { Divider } from '@ui-kitten/components'
import { Spacer } from '../../../../../../ui/atoms'
import { OwnerIcon } from '../../../../GeneralApplication/Application/components/OwnerIcon'
import { useTranslation } from 'react-i18next'
import { FlowController } from '../../../../GeneralApplication/Application/FlowController'
import { IDraftModel } from '@linqpal/models'
import { OwnerReviewStyles as styles } from '../../../../GeneralApplication/Application/flow/review/components/OwnerReviewStyles'
import { OwnerPropertyRow } from '../../../../GeneralApplication/Application/flow/review/components/OwnerPropertyRow'
import { CoOwner } from './CoOwner'
import { observer } from 'mobx-react'
import { formatDomesticPhone } from '@linqpal/models/src/helpers/phoneFormatter'
import ApplicationStore from '../../../../GeneralApplication/Application/ApplicationStore'

interface CoOwnersReviewProps {
  coOwners: CoOwner[]
  doc: IDraftModel
  flowController: FlowController
}

export const CoOwnersReview: FC<CoOwnersReviewProps> = observer(
  ({ coOwners, doc, flowController }) => {
    const { t } = useTranslation('application')

    return (
      <>
        {coOwners.map((coOwner, index) => (
          <>
            <View style={styles.container} key={coOwner.key}>
              <OwnerIcon isOwnerFilled={coOwner.isFilled} />
              <View style={styles.properties}>
                {coOwner.firstName || coOwner.lastName || coOwner.entityName ? (
                  <BtText style={styles.ownerName}>
                    {coOwner.type === OwnerTypes.INDIVIDUAL
                      ? `${coOwner.firstName} ${coOwner.lastName}`
                      : coOwner.entityName}
                  </BtText>
                ) : null}
                <View>
                  <OwnerPropertyRow
                    label={t('Preview.CoOwner')}
                    value={t('Preview.Percentage', {
                      percentage: coOwner.percentOwned,
                    })}
                  />
                  <OwnerPropertyRow
                    label={t(
                      coOwner.type === OwnerTypes.INDIVIDUAL
                        ? 'CoOwners.HomeAddress'
                        : 'Preview.LegalAddress',
                    )}
                    value={formatAddress(coOwner)}
                  />
                  {coOwner.type === OwnerTypes.INDIVIDUAL && (
                    <OwnerPropertyRow
                      label={t('CoOwners.Birthday')}
                      value={coOwner.birthday}
                    />
                  )}
                  {coOwner.type === OwnerTypes.INDIVIDUAL && (
                    <OwnerPropertyRow
                      label={t('CoOwners.SSN')}
                      value={coOwner.ssn}
                      secured
                    />
                  )}
                  {coOwner.type === OwnerTypes.ENTITY && (
                    <OwnerPropertyRow
                      label={t('Preview.EIN')}
                      value={coOwner.ein}
                      secured
                    />
                  )}
                  <OwnerPropertyRow
                    label={t('CoOwners.PhoneNumber')}
                    value={formatDomesticPhone(coOwner.phone)}
                  />
                  <OwnerPropertyRow
                    label={t('CoOwners.EmailAddress')}
                    value={coOwner.email}
                  />
                  {coOwner.type === OwnerTypes.ENTITY && (
                    <OwnerPropertyRow
                      label={t('Preview.AuthorizedRepresentative')}
                      value={`${coOwner.firstName} ${coOwner.lastName}`}
                    />
                  )}
                </View>
              </View>
              <View style={styles.editButtonContainer}>
                <BtButton
                  appearance={'ghost'}
                  size={'small'}
                  status={'basic'}
                  onPress={() => {
                    ApplicationStore.setPreviousStep(doc.current)
                    ApplicationStore.setCurrentCoOwnerIndex(index)
                    flowController.editSection('coOwnerInfo')
                  }}
                  // eslint-disable-next-line i18next/no-literal-string
                  iconLeft="edit-outline"
                >
                  {t('Review.Edit')}
                </BtButton>
              </View>
            </View>
            <Spacer height={20} />
            <Divider />
          </>
        ))}
      </>
    )
  },
)
