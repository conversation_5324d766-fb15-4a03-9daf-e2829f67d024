import {
  BankAccount,
  Company,
  CustomerAccount,
  Invitation,
  Invoice,
} from '../../models'
import { getPaymentNotificationReceivers } from '../invoices.service/notifications'
import { emailService } from '../email.service'
import EmailNotifications from '../../helpers/EmailNotifications'
import { Logger } from '../logger/logger.service'
import { formatCardDisplay } from '../../helpers/paymentMethodFormatter'
import { IBankAccount, IInvoice, IUser } from '../../models/types'
import {
  invitationTypes,
  PAYMENT_METHODS,
} from '@linqpal/models/src/dictionaries'
import { getApiURL } from '../../helpers/common'
import { CriticalError } from '@linqpal/models/src/types/exceptions'
import mongoose, { ClientSession } from 'mongoose'
import moment from 'moment'
import { getBranding } from '../branding.service'
import { isFactoringInvoice } from './invoicePostPaymentActions.service'

export class PaymentNotifications {
  private static logger = new Logger({
    module: 'PaymentNotifications',
  })

  static async invoiceProcessingToSupplier(
    invoice_id: string,
    totalProcessingAmount: number,
    paymentType: string,
  ) {
    const invoice = await Invoice.findById(invoice_id)
    if (!invoice || !invoice.customer_account_id || isFactoringInvoice(invoice))
      return

    const [customer, emails] = await Promise.all([
      CustomerAccount.findById(invoice.customer_account_id),
      getPaymentNotificationReceivers(invoice),
    ])

    if (emails) {
      try {
        await emailService.send({
          to: emails,
          ...EmailNotifications.invoiceProcessingToSupplier({
            customerName: customer?.name || '',
            invoiceNumber: invoice.invoice_number,
            invoiceAmount: invoice.total_amount,
            totalProcessingAmount,
            paymentType,
          }),
        })
      } catch (e) {
        this.logger.error({ err: e, emails }, 'failed sending email')
      }
    }
  }

  static async invoiceProcessingToCustomer(
    invoice: IInvoice,
    // bankAccount id or tabapay account id may come from UI, review and unify someday
    paymentAccountId: string,
    // pass payment method explicitly, not sure if there is a reliable way to detect it by the provided paymentAccountId
    paymentMethodType: 'ach' | 'card',
    serviceFee: number,
    paymentAmount: number,
    totalPaid: number,
    session: ClientSession | null,
  ) {
    const [supplier, customerAccount] = await Promise.all([
      Company.findById(invoice.company_id).session(session),
      CustomerAccount.findById(invoice.customer_account_id).session(session),
    ])

    if (!supplier) {
      this.logger.warn({ invoice }, 'no supplier found')
      return
    }

    const email = customerAccount?.isGuest
      ? customerAccount.guestInfo?.email
      : customerAccount?.email

    if (!email) {
      this.logger.warn({ invoice, customerAccount }, 'no email found')
      return
    }

    let paymentMethod = ''

    if (paymentMethodType === 'card') {
      const bankAccount = await BankAccount.findOne(
        mongoose.isValidObjectId(paymentAccountId)
          ? { _id: paymentAccountId }
          : { 'cardMetadata.accountId': paymentAccountId },
      ).session(session)

      paymentMethod = bankAccount?.cardMetadata?.lastFour
        ? `Card ending in ${bankAccount.cardMetadata.lastFour}`
        : 'Card'
    } else if (paymentMethodType === PAYMENT_METHODS.ACH) {
      paymentMethod = 'ACH'
    }

    await emailService.sendTo(
      email,
      EmailNotifications.invoiceProcessingToCustomer({
        supplierName: supplier.legalName || supplier.name || '',
        invoiceNumber: invoice.invoice_number,
        invoiceAmount: invoice.total_amount,
        invoiceDueDate: invoice.invoice_due_date,
        serviceFee,
        paymentAmount,
        paymentMethod,
        totalPaid,
        logoUrl: supplier.settings?.email?.logoUrl,
      }),
    )
  }

  static async invoiceProcessingToGuestPayer(
    invoice: IInvoice,
    guestUser: IUser,
    guestCompanyName: string,
    bankAccount: IBankAccount,
    serviceFee: number,
    totalPaid: number,
  ) {
    // prettier-ignore
    if (!guestUser.guestInfo) throw new CriticalError('no guestInfo', {guestUser})

    const supplier = await Company.findById(invoice.company_id)

    if (!supplier) {
      this.logger.warn({ invoice }, 'no supplier found')
      return
    }

    const paymentMethod = `Card ending in ${bankAccount.cardMetadata.lastFour}`

    const invitation = await Invitation.create({
      firstName: guestUser.firstName,
      lastName: guestUser.lastName,
      login: guestUser.guestInfo.email,
      phone: guestUser.guestInfo.phone,
      email: guestUser.guestInfo.email,
      company_id: null,
      type: invitationTypes.GUEST_PAYER_INVITE,
      role: 'Owner',
      metadata: {
        guestCustomerId: invoice.customer_account_id,
        businessName: guestCompanyName,
      },
    })

    const branding = await getBranding({
      companyId: invoice.company_id,
    })

    const host = branding?.host || getApiURL()

    const signupLink = `${host}/invitation?code=${invitation._id}`

    await emailService.sendTo(
      guestUser.guestInfo.email,
      EmailNotifications.invoiceProcessingToGuestPayer({
        supplierName: supplier.legalName || supplier.name || '',
        invoiceNumber: invoice.invoice_number,
        invoiceAmount: invoice.total_amount,
        invoiceDueDate: invoice.invoice_due_date,
        serviceFee,
        totalPaid,
        paymentMethod,
        signupLink,
        logoUrl: supplier.settings?.email?.logoUrl,
      }),
    )
  }

  static async ihcAutopayPaymentProcessingToCustomer(
    invoice: IInvoice,
    paymentAmount: number,
    processingFee: number,
    totalCharged: number,
    paymentAccountId: string,
  ) {
    if (!invoice.customer_account_id) return

    const customerAccount = await CustomerAccount.findById(
      invoice.customer_account_id,
    )
    if (!customerAccount?.email) return

    const paymentMethod = await formatCardDisplay(paymentAccountId)

    try {
      await emailService.sendTo(
        customerAccount.email,
        EmailNotifications.ihcAutopayPaymentProcessingToCustomer({
          customerName: customerAccount.first_name || '',
          invoiceNumber: invoice.invoice_number,
          paymentDate: moment().toDate(),
          amountCharged: paymentAmount,
          processingFee,
          totalCharged,
          paymentMethod,
        }),
      )
    } catch (e) {
      this.logger.error(
        { err: e, customerEmail: customerAccount.email },
        'failed sending IHC autopay payment processing email',
      )
    }
  }

  static async ihcAutopayPaymentFailedToCustomer(
    invoice: IInvoice,
    paymentAmount: number,
    paymentAccountId: string,
    failureReason?: string,
  ) {
    if (!invoice.customer_account_id) return

    const customerAccount = await CustomerAccount.findById(
      invoice.customer_account_id,
    )
    if (!customerAccount?.email) return

    const paymentMethod = await formatCardDisplay(paymentAccountId)

    try {
      await emailService.sendTo(
        customerAccount.email,
        EmailNotifications.ihcAutopayPaymentFailedToCustomer({
          customerName: customerAccount.first_name || '',
          invoiceNumber: invoice.invoice_number,
          paymentDate: moment().toDate(),
          amount: paymentAmount,
          paymentMethod,
          failureReason,
        }),
      )
    } catch (e) {
      this.logger.error(
        { err: e, customerEmail: customerAccount.email },
        'failed sending IHC autopay payment failed customer email',
      )
    }
  }

  static async ihcAutopayPaymentFailedToOps(
    invoice: IInvoice,
    failureReason?: string,
  ) {
    if (!invoice.customer_account_id) return

    const customerAccount = await CustomerAccount.findById(
      invoice.customer_account_id,
    )
    if (!customerAccount?.email) return

    const to =
      process.env.LP_MODE === 'prod'
        ? emailService.EMAILS.PRODUCT_OPERATION_TEAM
        : emailService.EMAILS.PRODUCT_OPERATION_TEAM_TEST

    try {
      await emailService.send({
        to,
        ...EmailNotifications.ihcAutopayPaymentFailedToOps({
          customerName: customerAccount.name || '',
          customerEmail: customerAccount.email,
          invoiceNumber: invoice.invoice_number,
          invoiceAmount: invoice.total_amount,
          failureReason,
          failureDate: moment().toDate(),
        }),
      })
    } catch (e) {
      this.logger.error(
        { err: e, to },
        'failed sending IHC autopay payment failed ops email',
      )
    }
  }
}
