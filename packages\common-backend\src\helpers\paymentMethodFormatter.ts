import mongoose from 'mongoose'
import { BankAccount } from '../models'
import { Logger } from '../services/logger/logger.service'

const logger = new Logger({
  module: 'PaymentMethodFormatter',
})

/**
 * Formats a payment method display string for notifications and UI
 * @param paymentAccountId - The TabaPay account ID to look up
 * @returns Formatted string like "Visa •••• 1234" or "Card •••• ****"
 */
export const formatCardDisplay = async (
  paymentAccountId: string,
): Promise<string> => {
  try {
    const bankAccount = await BankAccount.findOne(
      mongoose.isValidObjectId(paymentAccountId)
        ? { _id: paymentAccountId }
        : { 'cardMetadata.accountId': paymentAccountId },
    )

    if (bankAccount?.cardMetadata) {
      const { network, lastFour } = bankAccount.cardMetadata
      const cardNetwork = network || 'Card'
      const maskedNumber = lastFour ? `•••• ${lastFour}` : '•••• ****'
      return `${cardNetwork} ${maskedNumber}`
    }

    return 'Card •••• ****'
  } catch (error) {
    logger.warn(
      { error, paymentAccountId },
      'Failed to format payment method display',
    )
    return 'Card •••• ****'
  }
}
