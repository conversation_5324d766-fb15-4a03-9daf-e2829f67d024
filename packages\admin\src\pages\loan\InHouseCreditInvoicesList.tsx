import CIcon from '@coreui/icons-react'
import {
  CAlert,
  CButton,
  CCard,
  CCardBody,
  CCardFooter,
  CCardHeader,
  CCardTitle,
  CDataTable,
  CLabel,
  CPagination,
  CSpinner,
  CTooltip,
  CFormGroup,
  CInputCheckbox,
  CForm,
  CInput,
} from '@coreui/react'
import { observer } from 'mobx-react'
import moment from 'moment-timezone'
import numbro from 'numbro'
import React, { useCallback, useEffect, useState } from 'react'

import LoanStatusFilter from './components/LoanStatusFilter'
import InHouseCreditInvoicesListStore from './InHouseCreditInvoicesListStore'
import { LOAN_STATUS } from '@linqpal/models/src/dictionaries/loanStatuses'

export const GetLabelWithInfo: React.FC<{
  info: string
  col?: boolean
}> = ({ children, info, col }) => {
  return (
    <CLabel col={col}>
      {children}
      <CTooltip content={info} placement="top">
        <CIcon name="cil-info" size="sm" style={{ marginLeft: 4 }} />
      </CTooltip>
    </CLabel>
  )
}

const fields = [
  { key: 'companyName', sorter: false },
  { key: 'merchantName', sorter: false },
  {
    key: 'amount',
    sorter: { external: true, resetable: true },
  },
  {
    key: 'nextPaymentAmount',
    label: (
      <GetLabelWithInfo info="Includes late amount">
        Next Payment Amount
      </GetLabelWithInfo>
    ),
    sorter: false,
    _classes: 'text-right',
  },
  {
    key: 'nextPaymentDate',
    label: 'Next Payment Date',
    sorter: false,
  },
  {
    key: 'lateAmount',
    label: (
      <GetLabelWithInfo info="Sum of all Past Due amount">
        Late Amount
      </GetLabelWithInfo>
    ),
    sorter: false,
    _classes: 'text-right',
  },
  { key: 'lastPaymentDate', sorter: false },
  { key: 'term', sorter: false },
  { key: 'repaymentStatus', sorter: false },
  { key: 'lmsStatus', sorter: false },
  { key: 'daysLate', sorter: false },
  { key: 'lmsId', sorter: false },
  { key: 'loanPackage', sorter: false },
]

const Amount = ({ value }) => (
  <td className="text-monospace text-right">
    {parseFloat(value) >= 0
      ? numbro(value).formatCurrency({
          mantissa: 2,
          thousandSeparated: true,
        })
      : null}
  </td>
)

const Date = ({
  value,
  unix = false,
  format = 'MM/DD/YYYY',
  highlight = false,
}) => (
  <td className={highlight ? 'text-danger' : ''}>
    {!value
      ? null
      : unix
      ? moment.unix(+value.replace(/\D+/g, '')).format(format)
      : moment(value).format(format)}
  </td>
)

const scopedSlots = {
  amount: (item) => {
    return <Amount value={item.amount || 0} />
  },
  nextPaymentAmount: (item) => {
    return <Amount value={item.loanDetails.nextPaymentAmount || 0} />
  },
  issueDate: (item) => {
    return (
      <Date value={item.issueDate} unix={false} format="MM/DD/YYYY hh:mm A" />
    )
  },
  approvedAmount: (item) => <Amount value={item.approvedAmount} />,
  usedAmount: (item) => (
    <Amount value={item.usedAmount || item.approvedAmount} />
  ),
  nextPaymentDate: (item) => <Date value={item.loanDetails.nextPaymentDate} />,
  lateAmount: (item) => <Amount value={item.loanDetails.lateAmount || 0} />,
  lastPaymentDate: (item) => <Date value={item.lastPaymentDate} />,
  repaymentStatus: (item) => (
    <td>
      {item.status === LOAN_STATUS.CLOSED
        ? 'Paid off'
        : item.isAnyPaymentMissed
        ? 'Late'
        : 'Current'}
      {item.isAutoCollectionPaused ? ' (paused)' : ''}
    </td>
  ),
  term: (item) => <td>{item.term}</td>,
  daysLate: (item) => (
    <td className="text-center">{item.loanDetails.daysLate}</td>
  ),
  loanPackage: (item) => <td>{item.loanPackage}</td>,
  lmsStatus: (item) => <td>{item.status}</td>,
  lmsId: (item) => <td>{item.id}</td>,
  merchantName: (item) => <td>{item.merchantName ?? '-'}</td>,
}

export default observer(function InHouseCreditInvoicesList({
  history,
  location,
}): React.ReactElement {
  const {
    loanStatus,
    pageNumber,
    setPage,
    pageSize,
    pages,
    loading,
    items,
    message,
    fetchData,
    setLoanStatusFilter,
    showLateOnly,
    setLateOnly,
    sorter,
    setSorter,
    search,
    setSearch,
  } = InHouseCreditInvoicesListStore

  const { asc, column } = sorter

  const [refresh, setRefresh] = useState(0)
  const onRefresh = useCallback(() => setRefresh((r) => r + 1), [])

  useEffect(
    () => fetchData().cancel,
    [fetchData, pageNumber, pageSize, refresh, loanStatus, asc, column, search],
  )

  const toggleLateOnly = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      e.preventDefault()
      setLateOnly(!showLateOnly)
      fetchData()
    },
    [showLateOnly, setLateOnly, fetchData],
  )

  const onLoanStatusFilterSet = useCallback(
    (loanStatusFilter: string) => {
      setLoanStatusFilter(loanStatusFilter)
    },
    [setLoanStatusFilter],
  )

  const onChangeSearch = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(e.target.value)
    },
    [setSearch],
  )

  const onSubmitSearch = useCallback((e: React.FormEvent) => {
    e.preventDefault()
  }, [])

  return (
    <div>
      <CCard>
        <CCardHeader className="d-flex flex-row align-items-center">
          <CCardTitle className="mb-0" style={{ flex: '2 1 0%' }}>
            IHC Invoices
          </CCardTitle>
          <CFormGroup variant="checkbox" className="mr-3">
            <CInputCheckbox checked={showLateOnly} onChange={toggleLateOnly} />
            <CLabel variant="checkbox">Show late only</CLabel>
          </CFormGroup>
          <LoanStatusFilter onLoanStatusFilterSet={onLoanStatusFilterSet} />
          <CForm style={{ flex: '1 1 auto' }} onSubmit={onSubmitSearch}>
            <CInput
              value={search}
              onChange={onChangeSearch}
              placeholder="Type to search by Merchant Name or Invoice Number..."
            />
          </CForm>
          <CButton onClick={onRefresh} className="ml-2">
            Refresh
          </CButton>
        </CCardHeader>
        <CCardBody>
          <CDataTable
            fields={fields}
            items={items.slice()}
            loading={items.length === 0 && loading}
            scopedSlots={scopedSlots}
            clickableRows
            border
            hover
            size="sm"
            striped
            sorter={sorter}
            sorterValue={sorter}
            onSorterValueChange={setSorter}
            onRowClick={(item) =>
              history.push(`${location.pathname}/${item.id}`)
            }
          />
          <CAlert show={!!message} color="warning">
            {message}
          </CAlert>
        </CCardBody>
        {pages > 0 && (
          <CCardFooter>
            <div className="d-flex flex-row">
              <CPagination
                activePage={+pageNumber}
                limit={+pageSize}
                pages={pages}
                onActivePageChange={setPage}
              />
              {items.length > 0 && loading && (
                <div className="ml-3 mt-2">
                  <CSpinner size="sm" />
                </div>
              )}
            </div>
          </CCardFooter>
        )}
      </CCard>
    </div>
  )
})
