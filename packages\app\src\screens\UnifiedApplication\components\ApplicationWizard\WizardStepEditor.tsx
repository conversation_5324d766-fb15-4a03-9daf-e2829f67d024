import React, { FC, useEffect, useMemo, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { observer } from 'mobx-react'

import { View } from 'react-native'
import RootStore from '../../../../store/RootStore'
import ApplicationStore from '../../../GeneralApplication/Application/ApplicationStore'
import { useResponsive } from '../../../../utils/hooks'
import { FlowController } from '../../../GeneralApplication/Application/FlowController'
import { WizardHeader } from './WizardHeader'
import { CloseApplicationAlert } from '../../../GeneralApplication/Application/components'
import { Spacer } from '../../../../ui/atoms'
import { WizardGroupTitle } from './WizardGroupTitle'
import { useUnifiedApplication } from '../../UnifiedApplicationContext'
import {
  getApplicationStepEditor,
  IApplicationStepEditor,
} from '../../flow/ApplicationEditorSelector'
import { WizardStepTitle } from './WizardStepTitle'
import { WizardStepDescription } from './WizardStepDescription'
import { WizardDesktopButtons } from './WizardDesktopButtons'
import { WizardBackButton } from './WIzardBackButton'
import { MobileButtons } from './MobileButtons'
import { WizardScrollArea } from './WizardScrollArea'

export const WizardStepEditor: FC = observer(() => {
  const { t } = useTranslation('application')
  const [displayCloseAlert, setDisplayCloseAlert] = useState(false)
  const { isBusy } = RootStore
  const { isOptionalStep } = ApplicationStore
  const { sm } = useResponsive()

  const document = RootStore.userStore?.document

  const flowController = useMemo(() => new FlowController(document), [document])

  const currentPath = flowController.getCurrentStep(document)

  const [_, itemName] = currentPath.split('.')

  const onCloseApplication = () => {
    setDisplayCloseAlert(false)

    ApplicationStore.setCurrentCoOwnerIndex(-1)
  }

  const flowStep = RootStore.userStore?.document?.current

  /// ^^^-- old version
  /// vvv-- new version
  // TODO: VK: Unified: hide title for review.agreement
  const store = useUnifiedApplication()

  // TODO: VK: Unified: review, replace isOptionalStep with field-specific validation
  const disableNext = !store.isCurrentStepValid && !isOptionalStep(flowStep)

  // TODO: VK: change editor inside store
  const editor: IApplicationStepEditor = useMemo(
    () =>
      store.currentStep
        ? getApplicationStepEditor(store.currentStep)
        : { options: {}, component: () => <></> },
    [store.currentStep],
  )

  useEffect(() => {
    store.setEditorOptions(editor.options ?? {})
  }, [store, editor.options])

  console.log('wizard step', {
    currentGroup: store.currentGroup,
    currentStep: store.currentStep,
    isValid: store.isCurrentStepValid,
    editor,
    draft: store.document,
  })

  // TODO: VK: Unified: review impact of flowController.canMoveToPreviousStep() on back button behavior

  return (
    <View style={{ flex: 1 }}>
      <WizardHeader onClose={() => setDisplayCloseAlert(true)} />
      <WizardGroupTitle />

      <WizardScrollArea>
        {sm && store.editorOptions.canGoBack && (
          <>
            <Spacer height={20} />
            <WizardBackButton />
          </>
        )}

        <Spacer height={sm ? 20 : 10} />

        <WizardStepTitle />
        <WizardStepDescription />

        <editor.component />

        {!!sm && <WizardDesktopButtons />}
      </WizardScrollArea>

      {!sm && store.editorOptions.canMoveNext && (
        <MobileButtons
          t={t}
          sm={sm}
          next={true}
          onNext={() => store.moveNext()}
          onBack={() => {}}
          document={document}
          currentItem={itemName}
          disableNext={disableNext}
          isBusy={isBusy}
          canSubmit={store.canSubmit}
        />
      )}

      {displayCloseAlert && (
        <CloseApplicationAlert
          onCloseAlert={() => setDisplayCloseAlert(false)}
          onCloseApplication={onCloseApplication}
        />
      )}
    </View>
  )
})

// const styles = StyleSheet.create({
//   contentWrapper: {},
// })
